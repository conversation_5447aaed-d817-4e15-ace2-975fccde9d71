#!/usr/bin/env python3
"""
Interactive Input Test
======================

This script demonstrates and tests the input propagation through _on_field_changed
with correct type conversion for both form classes.
"""

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from components.forms.client_profile_form import ClientProfileForm
from components.forms.project_params_form import ProjectParamsForm


def test_client_profile_form_propagation():
    """Test ClientProfileForm input propagation and type conversion."""
    print("=" * 60)
    print("Testing ClientProfile Form Input Propagation")
    print("=" * 60)
    
    # Create client profile and form
    client_profile = ClientProfile()
    form = ClientProfileForm(client_profile)
    
    # Track data changes
    data_changes = []
    
    def capture_data_change(field_name, value):
        data_changes.append((field_name, value, type(value)))
        print(f"  {field_name}: {value} (type: {type(value).__name__})")
    
    form.on_data_changed = capture_data_change
    
    print("\n1. Testing string field changes:")
    form._on_field_changed('company_name', 'Test Company Ltd.')
    form._on_field_changed('client_name', '<PERSON>')
    form._on_field_changed('project_name', 'Solar Farm Project')
    form._on_field_changed('contact_email', '<EMAIL>')
    
    print("\n2. Testing capacity field with various inputs:")
    # Create mock event objects for capacity testing
    class MockEvent:
        def __init__(self, value):
            self.control = type('obj', (object,), {'value': value})()
    
    print("  Valid numeric input:")
    form._on_capacity_changed(MockEvent("25.5"))
    
    print("  Empty input:")
    form._on_capacity_changed(MockEvent(""))
    
    print("  Invalid input (should be ignored):")
    form._on_capacity_changed(MockEvent("invalid"))
    
    print(f"\nTotal captured changes: {len(data_changes)}")
    
    # Verify types
    print("\n3. Verifying type conversion:")
    for field_name, value, value_type in data_changes:
        expected_type = str if field_name != 'project_capacity_mw' else (float, type(None))
        if isinstance(expected_type, tuple):
            type_ok = any(isinstance(value, t) for t in expected_type)
        else:
            type_ok = isinstance(value, expected_type)
        
        status = "✓" if type_ok else "✗"
        print(f"  {status} {field_name}: {value_type.__name__}")
    
    return data_changes


def test_project_params_form_propagation():
    """Test ProjectParamsForm input propagation and type conversion."""
    print("\n" + "=" * 60)
    print("Testing ProjectParams Form Input Propagation")
    print("=" * 60)
    
    # Create project assumptions and form
    assumptions = EnhancedProjectAssumptions()
    form = ProjectParamsForm(assumptions)
    
    # Track data changes
    data_changes = []
    
    def capture_data_change(field_name, value):
        data_changes.append((field_name, value, type(value)))
        print(f"  {field_name}: {value} (type: {type(value).__name__})")
    
    form.on_data_changed = capture_data_change
    
    print("\n1. Testing numeric field changes:")
    form._on_field_changed('capacity_mw', '15.5')
    form._on_field_changed('capex_meur', '12.0')
    form._on_field_changed('production_mwh_year1', '27000')
    
    print("\n2. Testing percentage field changes (should convert to decimals):")
    form._on_field_changed('interest_rate', '7.5')  # 7.5% -> 0.075
    form._on_field_changed('discount_rate', '8.0')  # 8.0% -> 0.08
    form._on_field_changed('degradation_rate', '0.5')  # 0.5% -> 0.005
    
    print("\n3. Testing integer field changes:")
    form._on_field_changed('project_life_years', '25')
    form._on_field_changed('debt_years', '15')
    
    print("\n4. Testing string field changes:")
    form._on_field_changed('project_location', 'Morocco')
    
    print("\n5. Testing edge cases:")
    print("  Empty numeric input:")
    form._on_field_changed('capacity_mw', '')
    
    print("  Empty integer input:")
    form._on_field_changed('project_life_years', '')
    
    print("  Invalid numeric input (should be ignored):")
    form._on_field_changed('capex_meur', 'invalid')
    
    print(f"\nTotal captured changes: {len(data_changes)}")
    
    # Verify types and conversion
    print("\n6. Verifying type conversion:")
    expected_types = {
        'capacity_mw': float,
        'capex_meur': float,
        'production_mwh_year1': float,
        'interest_rate': float,
        'discount_rate': float,
        'degradation_rate': float,
        'project_life_years': int,
        'debt_years': int,
        'project_location': str
    }
    
    for field_name, value, value_type in data_changes:
        expected_type = expected_types.get(field_name, str)
        type_ok = isinstance(value, expected_type)
        status = "✓" if type_ok else "✗"
        print(f"  {status} {field_name}: {value_type.__name__} (expected: {expected_type.__name__})")
    
    # Check percentage conversion
    print("\n7. Verifying percentage conversion:")
    percentage_fields = ['interest_rate', 'discount_rate', 'degradation_rate']
    for field_name, value, value_type in data_changes:
        if field_name in percentage_fields:
            # These should be converted from percentages to decimals
            if field_name == 'interest_rate' and value == 0.075:
                print(f"  ✓ {field_name}: 7.5% -> 0.075")
            elif field_name == 'discount_rate' and value == 0.08:
                print(f"  ✓ {field_name}: 8.0% -> 0.08")
            elif field_name == 'degradation_rate' and value == 0.005:
                print(f"  ✓ {field_name}: 0.5% -> 0.005")
    
    return data_changes


def test_form_validation():
    """Test form validation functions."""
    print("\n" + "=" * 60)
    print("Testing Form Validation")
    print("=" * 60)
    
    # Test ClientProfile validation
    print("\n1. Testing ClientProfile validation:")
    client_profile = ClientProfile()
    form = ClientProfileForm(client_profile)
    
    print("  Empty profile validation:")
    errors = form.validate()
    print(f"    Errors found: {len(errors)}")
    for field, message in errors.items():
        print(f"    - {field}: {message}")
    
    # Fill in required fields
    client_profile.company_name = "Test Company"
    client_profile.client_name = "John Doe"
    client_profile.project_name = "Solar Project"
    client_profile.contact_email = "<EMAIL>"
    
    print("\n  Complete profile validation:")
    errors = form.validate()
    print(f"    Errors found: {len(errors)}")
    
    # Test ProjectAssumptions validation
    print("\n2. Testing ProjectAssumptions validation:")
    assumptions = EnhancedProjectAssumptions()
    form = ProjectParamsForm(assumptions)
    
    print("  Default assumptions validation:")
    errors = form.validate()
    print(f"    Errors found: {len(errors)}")
    
    # Test invalid values
    assumptions.capacity_mw = -5.0
    assumptions.debt_ratio = 1.5
    
    print("\n  Invalid assumptions validation:")
    errors = form.validate()
    print(f"    Errors found: {len(errors)}")
    for field, message in errors.items():
        print(f"    - {field}: {message}")


def main():
    """Run all interactive tests."""
    print("Interactive Input Propagation and Validation Test")
    print("=" * 60)
    
    # Test input propagation
    client_changes = test_client_profile_form_propagation()
    project_changes = test_project_params_form_propagation()
    
    # Test validation
    test_form_validation()
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"ClientProfile form captured {len(client_changes)} field changes")
    print(f"ProjectParams form captured {len(project_changes)} field changes")
    print("\nAll tests completed successfully!")
    print("✓ Input propagation working correctly")
    print("✓ Type conversion working correctly")
    print("✓ Form validation working correctly")


if __name__ == '__main__':
    main()
