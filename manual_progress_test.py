"""
Manual Progress Overlay Test
============================

This script demonstrates the three key requirements:
1. Overlay appears immediately
2. Percentage/text update every 0.5 s
3. Overlay closes gracefully at 100%

Run this script to manually verify the progress overlay behavior.
"""

import asyncio
import flet as ft
import time
from components.ui.enhanced_progress_overlay import EnhancedProgressOverlay


class ManualProgressTest:
    """Manual test class for progress overlay functionality."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.overlay = EnhancedProgressOverlay(page)
        self.test_running = False
        
    async def run_10_second_task(self):
        """Run a 10-second task with progress updates every 0.5 seconds."""
        if self.test_running:
            return
        
        self.test_running = True
        
        try:
            # 1. Verify overlay appears immediately
            print("TEST 1: Overlay should appear immediately")
            await self.overlay.show("Starting 10-second task...")
            
            # Verify overlay is visible
            assert self.overlay.is_visible, "Overlay should be visible immediately"
            print("✅ Overlay appeared immediately")
            
            # Wait a brief moment to see the initial state
            await asyncio.sleep(0.5)
            
            # 2. Update progress every 0.5 seconds
            print("TEST 2: Progress updates every 0.5 seconds")
            total_steps = 20  # 10 seconds / 0.5 seconds per step
            
            for step in range(1, total_steps + 1):
                progress = (step / total_steps) * 100
                elapsed = step * 0.5
                message = f"Processing step {step}/{total_steps} - {elapsed:.1f}s elapsed"
                
                # Update progress
                self.overlay.update_progress(progress, message)
                
                # Verify progress was updated
                assert self.overlay.progress_value == progress, f"Progress should be {progress}%"
                assert self.overlay.is_visible, "Overlay should remain visible during updates"
                
                print(f"✅ Progress updated: {progress:.1f}% - {message}")
                
                # Wait 0.5 seconds
                await asyncio.sleep(0.5)
            
            print("✅ All progress updates completed successfully")
            
            # 3. Verify overlay closes gracefully at 100%
            print("TEST 3: Overlay closes gracefully at 100%")
            
            # Ensure we're at 100%
            assert self.overlay.progress_value == 100, "Progress should be 100%"
            
            # Hide overlay
            await self.overlay.hide("10-second task completed successfully!")
            
            # Verify overlay is hidden
            assert not self.overlay.is_visible, "Overlay should be hidden after completion"
            print("✅ Overlay closed gracefully at 100%")
            
            # Show success message
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text("✅ All tests passed! Progress overlay working correctly."),
                bgcolor=ft.Colors.GREEN_400
            ))
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text(f"❌ Test failed: {e}"),
                bgcolor=ft.Colors.RED_400
            ))
        finally:
            self.test_running = False
    
    async def run_rapid_updates_test(self):
        """Test rapid progress updates to verify UI responsiveness."""
        if self.test_running:
            return
        
        self.test_running = True
        
        try:
            print("RAPID UPDATES TEST: Testing UI responsiveness")
            
            await self.overlay.show("Testing rapid updates...")
            
            # Rapid updates every 0.1 seconds for 5 seconds
            total_steps = 50  # 5 seconds / 0.1 seconds per step
            
            for step in range(1, total_steps + 1):
                progress = (step / total_steps) * 100
                message = f"Rapid update {step}/{total_steps}"
                
                self.overlay.update_progress(progress, message)
                await asyncio.sleep(0.1)
            
            await self.overlay.hide("Rapid updates test completed!")
            
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text("✅ Rapid updates test passed!"),
                bgcolor=ft.Colors.GREEN_400
            ))
            
        except Exception as e:
            print(f"❌ Rapid updates test failed: {e}")
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text(f"❌ Rapid updates test failed: {e}"),
                bgcolor=ft.Colors.RED_400
            ))
        finally:
            self.test_running = False
    
    async def run_stress_test(self):
        """Stress test with multiple show/hide cycles."""
        if self.test_running:
            return
        
        self.test_running = True
        
        try:
            print("STRESS TEST: Multiple show/hide cycles")
            
            for cycle in range(3):
                await self.overlay.show(f"Stress test cycle {cycle + 1}/3")
                
                # Quick progress updates
                for progress in range(0, 101, 25):
                    self.overlay.update_progress(progress, f"Cycle {cycle + 1} - {progress}%")
                    await asyncio.sleep(0.2)
                
                await self.overlay.hide(f"Cycle {cycle + 1} completed!")
                await asyncio.sleep(0.5)
            
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text("✅ Stress test passed!"),
                bgcolor=ft.Colors.GREEN_400
            ))
            
        except Exception as e:
            print(f"❌ Stress test failed: {e}")
            self.page.show_snack_bar(ft.SnackBar(
                content=ft.Text(f"❌ Stress test failed: {e}"),
                bgcolor=ft.Colors.RED_400
            ))
        finally:
            self.test_running = False


def main(page: ft.Page):
    """Main function to set up the manual test interface."""
    page.title = "Manual Progress Overlay Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 800
    page.window_height = 600
    
    # Create test instance
    test = ManualProgressTest(page)
    
    # Create test buttons
    def create_test_button(text, color, on_click):
        return ft.ElevatedButton(
            text=text,
            on_click=on_click,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=color,
                padding=ft.padding.all(15)
            ),
            width=300
        )
    
    # Test buttons
    main_test_button = create_test_button(
        "Run 10-Second Task Test",
        ft.Colors.BLUE,
        lambda _: page.run_task(test.run_10_second_task)
    )
    
    rapid_test_button = create_test_button(
        "Run Rapid Updates Test",
        ft.Colors.ORANGE,
        lambda _: page.run_task(test.run_rapid_updates_test)
    )
    
    stress_test_button = create_test_button(
        "Run Stress Test",
        ft.Colors.RED,
        lambda _: page.run_task(test.run_stress_test)
    )
    
    # Instructions
    instructions = ft.Text(
        """Manual Progress Overlay Test Instructions:
        
1. Click 'Run 10-Second Task Test' to test the main requirements:
   - Overlay appears immediately
   - Progress updates every 0.5 seconds
   - Overlay closes gracefully at 100%
   
2. Click 'Run Rapid Updates Test' to test UI responsiveness
   
3. Click 'Run Stress Test' to test multiple show/hide cycles
   
Watch the console output for detailed test results.""",
        size=14,
        color=ft.Colors.GREY_700
    )
    
    # Layout
    content = ft.Column([
        ft.Text("Manual Progress Overlay Test", size=28, weight=ft.FontWeight.BOLD),
        ft.Container(height=20),
        instructions,
        ft.Container(height=30),
        main_test_button,
        ft.Container(height=10),
        rapid_test_button,
        ft.Container(height=10),
        stress_test_button,
        ft.Container(height=20),
        ft.Text("Check console output for detailed test results", 
                size=12, color=ft.Colors.GREY_600)
    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    
    page.add(
        ft.Container(
            content=content,
            padding=50,
            expand=True,
            alignment=ft.alignment.center
        )
    )


if __name__ == "__main__":
    print("Starting Manual Progress Overlay Test...")
    print("=" * 50)
    ft.app(target=main)
