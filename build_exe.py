#!/usr/bin/env python3
"""
Build Script for Hiel Renewable Energy Financial Modeler
========================================================

This script builds a standalone executable (.exe) from the Flet application.
Supports both flet build (recommended) and PyInstaller as fallback.

Usage:
    python build_exe.py [--pyinstaller] [--debug]
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path
import json
import platform

# Application Configuration
APP_NAME = "Hiel RnE Modeler v3.0"
APP_SHORT_NAME = "HielRnEModeler"
APP_VERSION = "3.0.0"
APP_DESCRIPTION = "Professional Financial Modeling Tool for Renewable Energy Projects"
APP_COPYRIGHT = "© 2025 Hiel RnE Solutions"
MAIN_FILE = "main.py"
ICON_FILE = "assets/logo.ico"  # Place your logo here as .ico format

class BuildConfig:
    """Build configuration and utilities."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.assets_dir = self.project_root / "assets"
        
    def setup_directories(self):
        """Create necessary directories."""
        self.assets_dir.mkdir(exist_ok=True)
        print(f"✓ Assets directory ready: {self.assets_dir}")
        
        if not (self.assets_dir / "logo.ico").exists():
            print(f"⚠️  Icon file not found: {self.assets_dir / 'logo.ico'}")
            print("   Place your logo as 'assets/logo.ico' for app icon")
    
    def clean_build(self):
        """Clean previous build artifacts."""
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print("✓ Cleaned dist directory")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("✓ Cleaned build directory")
    
    def check_dependencies(self):
        """Check if required dependencies are installed."""
        try:
            import flet
            # Try to get version, but don't fail if not available
            try:
                version = getattr(flet, '__version__', 'installed')
                print(f"✓ Flet version: {version}")
            except:
                print("✓ Flet: installed")
        except ImportError:
            print("❌ Flet not installed. Run: pip install flet")
            return False
        
        # Check other critical dependencies
        required_packages = [
            'numpy', 'pandas', 'numpy_financial'
        ]
        
        missing = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package} installed")
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"❌ Missing packages: {', '.join(missing)}")
            print(f"   Run: pip install {' '.join(missing)}")
            return False
        
        return True

def build_with_flet(target_platform, debug=False):
    """Build using Flet's built-in build system (recommended)."""
    print(f"\n🔨 Building with Flet for {target_platform}...")

    # Platform-specific build target
    if target_platform == "windows":
        flet_target = "windows"
        icon_file = "assets/logo.ico"
    else:
        flet_target = "linux"
        icon_file = "assets/Hiel RnE Logo.png" if Path("assets/Hiel RnE Logo.png").exists() else None

    # Prepare flet build command
    cmd = [
        sys.executable, "-m", "flet", "build", flet_target,
        "--name", APP_SHORT_NAME,
        "--description", APP_DESCRIPTION,
        "--product", APP_NAME,
        "--version", APP_VERSION,
        "--copyright", APP_COPYRIGHT,
        "--onefile",
        "--no-console"
    ]
    if icon_file:
        cmd.extend(["--icon", str(icon_file)])
        print(f"✓ Using icon: {icon_file}")
    else:
        print("⚠️  No icon specified - using default")
    if debug:
        cmd.append("--verbose")
    cmd.extend(["--module-path", "."])
    cmd.extend(["--add-data", "app:app"])
    cmd.extend(["--add-data", "components:components"])
    cmd.extend(["--add-data", "config:config"])
    cmd.extend(["--add-data", "models:models"])
    cmd.extend(["--add-data", "services:services"])
    cmd.extend(["--add-data", "utils:utils"])
    cmd.extend(["--add-data", "views:views"])
    cmd.append(MAIN_FILE)
    print(f"Command: {' '.join(cmd)}")
    try:
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path.cwd())
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, env=env)
        print("✓ Flet build completed successfully!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Flet build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ Flet build command not found. Ensure Flet is properly installed.")
        return False

def build_with_pyinstaller(target_platform, debug=False):
    """Build using PyInstaller as fallback."""
    print(f"\n🔨 Building with PyInstaller for {target_platform} (fallback)...")
    try:
        import PyInstaller
        print(f"✓ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not installed. Run: pip install pyinstaller")
        return False
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name", APP_SHORT_NAME,
        f"--distpath={Path('dist').absolute()}",
        f"--workpath={Path('build').absolute()}",
        f"--paths={Path('.').absolute()}",
    ]
    if target_platform == "windows":
        icon_path = Path("assets/logo.ico")
    else:
        icon_path = Path("assets/Hiel RnE Logo.png") if Path("assets/Hiel RnE Logo.png").exists() else None
    if icon_path and icon_path.exists():
        cmd.extend(["--icon", str(icon_path.absolute())])
    # Add hidden imports for common issues
    hidden_imports = [
        "flet",
        "numpy",
        "pandas",
        "numpy_financial",
        "sqlite3",
        "logging",
        "json",
        "datetime",
        "dataclasses",
        "typing",
        "pathlib",
        "asyncio",
        "concurrent.futures",
        # Application modules
        "app",
        "app.app_controller",
        "app.app_state",
        "components",
        "components.charts",
        "components.charts.chart_generator",
        "components.forms",
        "components.forms.client_profile_form",
        "components.forms.project_assumptions_form",
        "components.ui",
        "components.ui.modern_components",
        "components.widgets",
        "components.widgets.enhanced_widgets",
        "config",
        "config.app_config",
        "config.ui_config",
        "config.export_config",
        "models",
        "models.client_profile",
        "models.location_config",
        "models.project_assumptions",
        "models.ui_state",
        "services",
        "services.enhanced_dcf_model",
        "services.financial_service",
        "services.export_service",
        "services.error_handler",
        "services.validation_service",
        "services.persistence_service",
        "services.enhanced_integration_service",
        "services.location_service",
        "services.report_service",
        "services.health_monitor",
        "utils",
        "utils.file_utils",
        "utils.formatting_utils",
        "utils.validation_utils",
        "views",
        "views.base_view",
        "views.dashboard_view",
        "views.financial_model_view",
        "views.export_view",
        "views.project_setup_view",
        "views.location_comparison_view",
        "views.validation_view",
        "views.sensitivity_view",
        "views.monte_carlo_view",
        "views.scenarios_view",
        "views.project_settings_view"
    ]

    for imp in hidden_imports:
        cmd.extend(["--hidden-import", imp])
    
    # Add data files and directories
    data_files = [
        ("config", "config"),
        ("data", "data"),
        ("logs", "logs"),
        ("ml_models", "ml_models"),
        ("quality_assurance", "quality_assurance"),
    ]
    
    # Platform-specific separator for --add-data
    separator = ';' if target_platform == "windows" else ':'
    
    for src, dst in data_files:
        if Path(src).exists():
            cmd.extend(["--add-data", f"{src}{separator}{dst}"])
    
    # Add template files if they exist
    template_dirs = [
        ("config/templates", "config/templates"),
    ]
    
    for src, dst in template_dirs:
        if Path(src).exists():
            cmd.extend(["--add-data", f"{src}{separator}{dst}"])
    
    if debug:
        cmd.append("--debug=all")
    
    cmd.append(MAIN_FILE)
    
    print(f"Command: {' '.join(cmd)}")

    try:
        # Set PYTHONPATH environment variable to include current directory
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path.cwd())

        result = subprocess.run(cmd, check=True, env=env)
        print("✓ PyInstaller build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller build failed: {e}")
        return False

def build_appimage(binary_path, appimage_name):
    """Package the Linux binary as an AppImage using appimagetool."""
    print(f"\n📦 Packaging {binary_path} as AppImage...")
    appdir = Path("dist/AppDir")
    appdir.mkdir(parents=True, exist_ok=True)
    (appdir / "usr/bin").mkdir(parents=True, exist_ok=True)
    shutil.copy2(binary_path, appdir / "usr/bin" / appimage_name)
    desktop_file = appdir / f"{appimage_name}.desktop"
    with open(desktop_file, "w") as f:
        f.write(f"""
[Desktop Entry]
Type=Application
Name={APP_NAME}
Exec={appimage_name}
Icon={appimage_name}
Comment={APP_DESCRIPTION}
Categories=Utility;
""")
    # Copy icon if available
    icon_src = Path("assets/Hiel RnE Logo.png")
    if icon_src.exists():
        shutil.copy2(icon_src, appdir / f"{appimage_name}.png")
    # Check for appimagetool
    if shutil.which("appimagetool") is None:
        print("❌ appimagetool not found. Please install it to build AppImage.")
        print("   See: https://github.com/AppImage/AppImageTool")
        return False
    # Build AppImage
    cmd = ["appimagetool", str(appdir), f"dist/{appimage_name}.AppImage"]
    print(f"Running: {' '.join(cmd)}")
    try:
        subprocess.run(cmd, check=True)
        print(f"✓ AppImage created: dist/{appimage_name}.AppImage")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ AppImage build failed: {e}")
        return False

def create_version_info():
    """Create version info file for Windows executable."""
    version_info = f"""# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({APP_VERSION.replace('.', ', ')}, 0),
    prodvers=({APP_VERSION.replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(u'040904B0', [
        StringStruct(u'CompanyName', u'Hiel RnE Solutions'),
        StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{APP_SHORT_NAME}'),
        StringStruct(u'LegalCopyright', u'{APP_COPYRIGHT}'),
        StringStruct(u'OriginalFilename', u'{APP_SHORT_NAME}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')
      ])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
    
    with open("version_info.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    
    print("✓ Created version info file")

def create_build_info():
    """Create build information file."""
    build_info = {
        "app_name": APP_NAME,
        "app_short_name": APP_SHORT_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION,
        "copyright": APP_COPYRIGHT,
        "build_date": str(Path(__file__).stat().st_mtime),
        "python_version": sys.version,
        "platform": sys.platform
    }
    
    with open("dist/build_info.json", "w") as f:
        json.dump(build_info, f, indent=2)
    
    print("✓ Created build info file")

def main():
    """Main build function."""
    parser = argparse.ArgumentParser(description="Build Hiel RnE Financial Modeler executable and portable Linux AppImage")
    parser.add_argument("--pyinstaller", action="store_true", help="Use PyInstaller instead of Flet build")
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    parser.add_argument("--clean", action="store_true", help="Clean build directories first")
    parser.add_argument("--platform", choices=["windows", "linux"], default="windows", help="Target platform (windows/linux)")
    parser.add_argument("--appimage", action="store_true", help="For Linux: package as AppImage after build")
    args = parser.parse_args()
    print(f"""
🏗️  Building {APP_NAME} v{APP_VERSION} for {args.platform}
{'='*60}
""")
    config = BuildConfig()
    config.setup_directories()
    if args.clean:
        config.clean_build()
    if not config.check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return 1
    if not Path(MAIN_FILE).exists():
        print(f"❌ Main file not found: {MAIN_FILE}")
        return 1
    create_version_info()
    success = False
    if args.pyinstaller:
        success = build_with_pyinstaller(args.platform, args.debug)
    else:
        success = build_with_flet(args.platform, args.debug)
        if not success:
            print("\n🔄 Flet build failed, trying PyInstaller...")
            success = build_with_pyinstaller(args.platform, args.debug)
    if success:
        Path("dist").mkdir(exist_ok=True)
        create_build_info()
        if args.platform == "linux" and args.appimage:
            # Find the built binary (Flet and PyInstaller both put it in dist/)
            bin_candidates = list(Path("dist").glob(f"{APP_SHORT_NAME}*"))
            bin_path = next((b for b in bin_candidates if b.is_file() and os.access(b, os.X_OK)), None)
            if bin_path:
                build_appimage(bin_path, APP_SHORT_NAME)
            else:
                print("❌ Could not find built Linux binary to package as AppImage.")
        print(f"""
✅ Build completed successfully!
""")
        return 0
    else:
        print("\n❌ Build failed! Check the error messages above.")
        return 1
if __name__ == "__main__":
    exit(main()) 