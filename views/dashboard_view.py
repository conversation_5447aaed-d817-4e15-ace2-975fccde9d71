"""
Dashboard View
==============

Enhanced dashboard view with comprehensive financial analysis visualization.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView
from components.charts.kpi_charts import KPICharts
from components.charts.cashflow_charts import CashflowCharts
from components.widgets.kpi_card import KPICard
from components.widgets.enhanced_features_panel import EnhancedFeaturesPanel, MLInsightsWidget
from services.model_reliability_service import ModelReliabilityService
from services.enhanced_integration_service import get_integration_service

# Enhanced UI/UX components
from components.ui.enhanced_loading_system import (
    create_skeleton, SkeletonType, create_loading_spinner, 
    LoadingVariant, ComponentSize as LoadingSize
)
from components.ui.accessibility_system import (
    make_accessible_button, make_accessible_card, 
    ScreenReaderSupport, get_accessibility_manager
)
from components.ui.modern_theme_system import ComponentSize


class DashboardView(BaseView):
    """Enhanced dashboard view for financial analysis."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
        self.ml_predictions: Optional[Dict[str, Any]] = None
        self.charts_3d: Optional[Dict[str, str]] = None
        self.kpi_charts = KPICharts()
        self.cashflow_charts = CashflowCharts()
        self.reliability_service = ModelReliabilityService()
        self.reliability_metrics: Optional[Dict[str, Any]] = None

        # Enhanced features
        self.integration_service = get_integration_service()
        self.enhanced_features_panel = EnhancedFeaturesPanel(
            on_feature_toggle=self._handle_feature_toggle,
            on_action=self._handle_feature_action
        )
        self.ml_insights_widget = MLInsightsWidget()
    
    def build_content(self) -> ft.Control:
        """Build the dashboard view content with enhanced loading and accessibility."""
        
        if not self.financial_results:
            # Show enhanced loading state with skeleton screens
            return self._create_enhanced_empty_state()
        
        # Enhanced Header with 2025 branding
        header = self.create_section_header(
            "Enhanced Financial Analytics Dashboard • 2025",
            "Comprehensive DCF analysis with advanced reliability metrics and trend analysis"
        )

        # Enhanced Features Panel (NEW)
        features_panel = self.enhanced_features_panel.build()
        self._update_features_status()

        # Enhanced KPI Summary Cards with DCF metrics
        kpi_summary = self._create_enhanced_kpi_summary()

        # ML Insights Widget (NEW)
        ml_insights = self._create_ml_insights_section()

        # Model Reliability Assessment
        reliability_assessment = self._create_reliability_assessment()

        # Financial Summary with DCF breakdown
        financial_summary = self._create_enhanced_financial_summary()

        # Advanced Charts Grid with trend analysis
        charts_grid = self._create_enhanced_charts_grid()

        # DCF Analysis Section
        dcf_analysis = self._create_dcf_analysis()

        # Grant Analysis
        grant_analysis = self._create_grant_analysis()

        # Enhanced Risk Analysis with 2025 standards
        risk_analysis = self._create_enhanced_risk_analysis()

        # Factor Analysis and Trends
        factor_analysis = self._create_factor_analysis()

        # 3D Charts Section (NEW)
        charts_3d_section = self._create_3d_charts_section()

        # Create main scrollable container with proper configuration
        main_content = ft.Column([
            header,
            features_panel,  # NEW: Enhanced features control panel
            ml_insights,     # NEW: ML insights section
            charts_3d_section,  # NEW: 3D charts section
            kpi_summary,
            reliability_assessment,
            financial_summary,
            charts_grid,
            dcf_analysis,
            grant_analysis,
            risk_analysis,
            factor_analysis,
            # Add some bottom padding for better scroll experience
            ft.Container(height=50)
        ], spacing=15, tight=True)
        
        # Wrap in a scrollable container with proper configuration
        return ft.Column(
            [main_content],
            expand=True,
            scroll=ft.ScrollMode.ALWAYS,
            spacing=0,
            tight=True,
            on_scroll=self._handle_scroll_event
        )
    
    def _get_fallback_financial_data(self) -> Dict[str, Any]:
        """Get fallback financial data when real data is not available."""
        return {
            'kpis': {
                'IRR_project': 0.10,  # 10%
                'IRR_equity': 0.15,   # 15%  
                'NPV_project': 1000000,  # 1M EUR
                'NPV_equity': 500000,    # 500k EUR
                'LCOE_eur_kwh': 0.045,   # 4.5 c€/kWh
                'Payback_years': 8.5,    # 8.5 years
                'Terminal_value': 2000000,  # 2M EUR
                'Min_DSCR': 1.25,
                'Avg_DSCR': 1.40
            },
            'cashflow': {},
            'assumptions': {
                'capex_meur': 8.5,
                'opex_keuros_year1': 180,
                'production_mwh_year1': 18000,
                'ppa_price_eur_kwh': 0.045,
                'project_life_years': 25,
                'grant_meur_italy': 0.5,
                'grant_meur_masen': 1.0,
                'grant_meur_connection': 0.3,
                'grant_meur_simest_africa': 0.2,
                'grant_meur_cri': 0.4
            },
            'is_fallback': True
        }

    def _create_enhanced_kpi_summary(self) -> ft.Container:
        """Create enhanced KPI summary cards with DCF metrics."""
        if not self.financial_results:
            self.logger.warning("No financial results available, using fallback data for KPI summary")
            self.financial_results = self._get_fallback_financial_data()

        kpis = self.financial_results.get('kpis', {})
        
        # If KPIs are empty, use fallback
        if not kpis:
            self.logger.warning("KPIs are empty, using fallback data")
            fallback_data = self._get_fallback_financial_data()
            kpis = fallback_data['kpis']
        
        # Debug: Log available KPIs
        self.logger.info(f"DEBUG: Creating KPI summary with KPIs: {kpis}")

        # Enhanced KPI cards with additional DCF metrics and fallback values
        kpi_cards = ft.Row([
            KPICard(
                title="Project IRR",
                value=f"{kpis.get('IRR_project', 0):.1%}" if kpis.get('IRR_project') is not None else "0.0%",
                color=ft.Colors.GREEN,
                icon=ft.Icons.TRENDING_UP,
                target_value=12.0,
                current_value=(kpis.get('IRR_project', 0) * 100) if kpis.get('IRR_project') is not None else 0
            ).build(),

            KPICard(
                title="Equity IRR",
                value=f"{kpis.get('IRR_equity', 0):.1%}" if kpis.get('IRR_equity') is not None else "0.0%",
                color=ft.Colors.BLUE,
                icon=ft.Icons.ACCOUNT_BALANCE,
                target_value=15.0,
                current_value=(kpis.get('IRR_equity', 0) * 100) if kpis.get('IRR_equity') is not None else 0
            ).build(),

            KPICard(
                title="NPV Project",
                value=self.format_currency(kpis.get('NPV_project', 0)) if kpis.get('NPV_project') is not None else "€0",
                color=ft.Colors.PURPLE,
                icon=ft.Icons.MONETIZATION_ON
            ).build(),

            KPICard(
                title="Terminal Value",
                value=f"€{kpis.get('Terminal_value', 0)/1e6:.1f}M" if kpis.get('Terminal_value') is not None and kpis.get('Terminal_value') != 0 else "€0.0M",
                color=ft.Colors.INDIGO,
                icon=ft.Icons.TIMELINE
            ).build(),

            KPICard(
                title="LCOE",
                value=f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh" if kpis.get('LCOE_eur_kwh') is not None else "0.000 €/kWh",
                color=ft.Colors.ORANGE,
                icon=ft.Icons.ELECTRIC_BOLT,
                target_value=0.045,
                current_value=kpis.get('LCOE_eur_kwh', 0) if kpis.get('LCOE_eur_kwh') is not None else 0,
                lower_is_better=True
            ).build(),

            KPICard(
                title="Payback Period",
                value=f"{kpis.get('Payback_years', 0):.1f} years" if kpis.get('Payback_years') is not None else "0.0 years",
                color=ft.Colors.CYAN,
                icon=ft.Icons.SCHEDULE
            ).build()
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND, wrap=True)
        
        return ft.Container(
            content=kpi_cards,
            padding=20,
            bgcolor=ft.Colors.GREY_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_reliability_assessment(self) -> ft.Card:
        """Create model reliability assessment section."""
        if not self.financial_results:
            return ft.Card()

        try:
            # Calculate reliability metrics
            self.reliability_metrics = self.reliability_service.assess_model_reliability(
                self.financial_results,
                self.financial_results.get('assumptions', {}),
                None,  # Monte Carlo results would be passed here
                None   # Sensitivity results would be passed here
            )

            reliability_report = self.reliability_service.generate_reliability_report(self.reliability_metrics)

            # Create reliability dashboard
            reliability_content = ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Overall Reliability", size=14, color=ft.Colors.GREY_600),
                            ft.Text(f"{reliability_report['overall_reliability_score']:.0f}/100",
                                   size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            ft.Text(reliability_report['reliability_grade'],
                                   size=12, color=ft.Colors.BLUE_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=150,
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Confidence Level", size=14, color=ft.Colors.GREY_600),
                            ft.Text(reliability_report['confidence_level'].split(' - ')[0],
                                   size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                            ft.Text("Model Reliability",
                                   size=12, color=ft.Colors.GREEN_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=150,
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("95% Confidence Interval", size=14, color=ft.Colors.GREY_600),
                            ft.Text(f"{self.reliability_metrics.confidence_interval_95[0]:.1%} - {self.reliability_metrics.confidence_interval_95[1]:.1%}",
                                   size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600),
                            ft.Text("IRR Equity Range",
                                   size=12, color=ft.Colors.PURPLE_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=180,
                        padding=15,
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=10
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),

                ft.Container(height=15),

                # Key strengths and improvements
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("✓ Key Strengths", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                            *[ft.Text(f"• {strength}", size=12, color=ft.Colors.GREY_700)
                              for strength in reliability_report['key_strengths'][:3]]
                        ]),
                        expand=1,
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8
                    ),
                    ft.Container(width=10),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("⚠ Areas for Improvement", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
                            *[ft.Text(f"• {improvement}", size=12, color=ft.Colors.GREY_700)
                              for improvement in reliability_report['areas_for_improvement'][:3]]
                        ]),
                        expand=1,
                        padding=15,
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8
                    )
                ])
            ])

            return self.create_card(
                "Model Reliability Assessment • 2025 Standards",
                reliability_content,
                icon=ft.Icons.VERIFIED_USER,
                bgcolor=ft.Colors.WHITE
            )

        except Exception as e:
            return self.create_card(
                "Model Reliability Assessment",
                ft.Text(f"Reliability assessment unavailable: {str(e)}", color=ft.Colors.GREY_600),
                icon=ft.Icons.WARNING,
                bgcolor=ft.Colors.GREY_50
            )
    
    def _create_enhanced_financial_summary(self) -> ft.Card:
        """Create enhanced financial summary section with DCF breakdown."""
        if not self.financial_results:
            self.logger.warning("No financial results available for summary, using fallback data")
            self.financial_results = self._get_fallback_financial_data()

        # Debug: Log the structure of financial_results
        self.logger.info(f"DEBUG: Financial results keys: {list(self.financial_results.keys())}")
        
        cashflow_data = self.financial_results.get('cashflow')
        kpis = self.financial_results.get('kpis', {})
        
        # Debug: Log KPIs and cashflow structure
        self.logger.info(f"DEBUG: KPIs available: {list(kpis.keys()) if kpis else 'No KPIs'}")
        self.logger.info(f"DEBUG: Cashflow type: {type(cashflow_data)}")
        
        if cashflow_data is None or (isinstance(cashflow_data, dict) and not cashflow_data):
            # If no cashflow data, try to create summary cards from KPIs only
            self.logger.warning("No cashflow data available, using KPIs for summary")
            
            # Calculate values from assumptions if available
            assumptions = self.financial_results.get('assumptions', {})
            
            # If assumptions are also empty, use fallback
            if not assumptions:
                self.logger.warning("No assumptions available, using fallback assumptions")
                fallback_data = self._get_fallback_financial_data()
                assumptions = fallback_data['assumptions']
                kpis = fallback_data['kpis']
            
            total_revenue = 0
            total_opex = 0
            total_capex = assumptions.get('capex_meur', 0)
            grant_cri = assumptions.get('grant_meur_cri', 0)
            total_grants = assumptions.get('grant_meur_italy', 0) + assumptions.get('grant_meur_masen', 0) + assumptions.get('grant_meur_connection', 0) + assumptions.get('grant_meur_simest_africa', 0) + grant_cri
            total_ebitda = 0
            terminal_value = kpis.get('Terminal_value', 0) / 1e6 if kpis.get('Terminal_value') else 0
            
            # Try to estimate some values from available data
            if 'production_mwh_year1' in assumptions and 'ppa_price_eur_kwh' in assumptions:
                annual_revenue = assumptions['production_mwh_year1'] * 1000 * assumptions['ppa_price_eur_kwh'] / 1e6
                total_revenue = annual_revenue * assumptions.get('project_life_years', 25)
            
            if 'opex_keuros_year1' in assumptions:
                annual_opex = assumptions['opex_keuros_year1'] / 1000  # Convert to M EUR
                total_opex = annual_opex * assumptions.get('project_life_years', 25)
        else:
            # Calculate enhanced summary metrics from cashflow
            import pandas as pd
            if isinstance(cashflow_data, dict):
                df = pd.DataFrame(cashflow_data)
            else:
                df = cashflow_data

            # Debug: Log DataFrame structure
            self.logger.info(f"DEBUG: Cashflow columns: {list(df.columns) if hasattr(df, 'columns') else 'No columns'}")
            
            # Enhanced metrics including DCF components
            total_revenue = df['Revenue'].sum() / 1e6 if 'Revenue' in df.columns else 0
            total_opex = abs(df['Total_OPEX'].sum()) / 1e6 if 'Total_OPEX' in df.columns else 0
            total_capex = abs(df['Capex'].sum()) / 1e6 if 'Capex' in df.columns else 0
            total_grants = df['Grants'].sum() / 1e6 if 'Grants' in df.columns else 0
            total_ebitda = df['EBITDA'].sum() / 1e6 if 'EBITDA' in df.columns else 0
            terminal_value = df['Terminal_Value'].sum() / 1e6 if 'Terminal_Value' in df.columns else 0
        
        # Enhanced summary cards with DCF metrics
        summary_cards = ft.Column([
            ft.Row([
                self._create_summary_card(
                    "Total Revenue",
                    f"€{total_revenue:.1f}M" if total_revenue > 0 else "€0.0M",
                    ft.Colors.GREEN,
                    ft.Icons.TRENDING_UP
                ),
                self._create_summary_card(
                    "Total EBITDA",
                    f"€{total_ebitda:.1f}M" if total_ebitda > 0 else "€0.0M",
                    ft.Colors.TEAL,
                    ft.Icons.ACCOUNT_BALANCE_WALLET
                ),
                self._create_summary_card(
                    "Total OPEX",
                    f"€{total_opex:.1f}M" if total_opex > 0 else "€0.0M",
                    ft.Colors.RED,
                    ft.Icons.TRENDING_DOWN
                ),
                self._create_summary_card(
                    "Total CAPEX",
                    f"€{total_capex:.1f}M" if total_capex > 0 else "€0.0M",
                    ft.Colors.BLUE,
                    ft.Icons.BUILD
                )
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),

            ft.Container(height=10),

            ft.Row([
                self._create_summary_card(
                    "Total Grants",
                    f"€{total_grants:.1f}M" if total_grants > 0 else "€0.0M",
                    ft.Colors.PURPLE,
                    ft.Icons.CARD_GIFTCARD
                ),
                self._create_summary_card(
                    "Terminal Value",
                    f"€{terminal_value:.1f}M" if terminal_value > 0 else "€0.0M",
                    ft.Colors.INDIGO,
                    ft.Icons.TIMELINE
                ),
                self._create_summary_card(
                    "EBITDA Margin",
                    f"{(total_ebitda/total_revenue*100) if total_revenue > 0 else 0:.1f}%",
                    ft.Colors.CYAN,
                    ft.Icons.PERCENT
                ),
                self._create_summary_card(
                    "Grant Coverage",
                    f"{(total_grants/total_capex*100) if total_capex > 0 else 0:.1f}%",
                    ft.Colors.ORANGE,
                    ft.Icons.SHIELD
                )
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        ])
        
        return self.create_card(
            "Enhanced Project Financial Summary • DCF Analysis",
            summary_cards,
            icon=ft.Icons.ACCOUNT_BALANCE_WALLET
        )
    
    def _create_summary_card(self, title: str, value: str, color: str, icon: str) -> ft.Container:
        """Create a summary metric card."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=color, size=30),
                ft.Text(title, size=12, color=ft.Colors.GREY_600),
                ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=color)
            ], alignment=ft.MainAxisAlignment.CENTER),
            width=120,
            height=100,
            padding=10,
            bgcolor=f"{color}10",  # Light background
            border_radius=8
        )
    
    def _create_charts_grid(self) -> ft.Container:
        """Create charts grid layout."""
        if not self.financial_results:
            return ft.Container()
        
        # Create chart components
        kpi_gauge_chart = self.kpi_charts.create_kpi_gauge_chart(self.financial_results)
        irr_comparison_chart = self.kpi_charts.create_irr_comparison_chart(self.financial_results)
        cashflow_timeline = self.cashflow_charts.create_cashflow_timeline_chart(self.financial_results)
        dscr_timeline = self.cashflow_charts.create_dscr_timeline_chart(self.financial_results)
        
        charts_grid = ft.Column([
            ft.Row([
                ft.Container(content=kpi_gauge_chart, expand=1),
                ft.Container(content=irr_comparison_chart, expand=1)
            ]),
            ft.Row([
                ft.Container(content=cashflow_timeline, expand=1),
                ft.Container(content=dscr_timeline, expand=1)
            ])
        ])
        
        return self.create_card(
            "Financial Analysis Charts",
            charts_grid,
            icon=ft.Icons.ANALYTICS
        )
    
    def _create_grant_analysis(self) -> ft.Card:
        """Create grant analysis section."""
        assumptions = self.financial_results.get('assumptions', {})
        
        # Calculate grant breakdown
        grant_italy = assumptions.get('grant_meur_italy', 0)
        grant_masen = assumptions.get('grant_meur_masen', 0)
        grant_connection = assumptions.get('grant_meur_connection', 0)
        grant_simest = assumptions.get('grant_meur_simest_africa', 0)
        grant_cri = assumptions.get('grant_meur_cri', 0)
        total_grants = grant_italy + grant_masen + grant_connection + grant_simest + grant_cri
        total_capex = assumptions.get('capex_meur', 1)
        
        grant_percentage = (total_grants / total_capex * 100) if total_capex > 0 else 0
        
        grant_breakdown = ft.Column([
            self.create_info_row("Italian Government Grant", f"€{grant_italy:.2f}M"),
            self.create_info_row("MASEN Strategic Grant", f"€{grant_masen:.2f}M"),
            self.create_info_row("Grid Connection Grant", f"€{grant_connection:.2f}M"),
            self.create_info_row("SIMEST African Fund", f"€{grant_simest:.2f}M"),
            self.create_info_row("CRI Regional Investment Center Grant", f"€{grant_cri:.2f}M"),
            ft.Divider(),
            self.create_info_row("Total Grants", f"€{total_grants:.2f}M", ft.Colors.GREEN),
            self.create_info_row("Grant Percentage", f"{grant_percentage:.1f}%", ft.Colors.GREEN),
        ])
        
        return self.create_card(
            "Grant Analysis",
            grant_breakdown,
            icon=ft.Icons.CARD_GIFTCARD,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _create_risk_analysis(self) -> ft.Card:
        """Create risk analysis section."""
        if not self.financial_results:
            return ft.Card()
        
        kpis = self.financial_results.get('kpis', {})
        
        # Risk indicators
        risk_indicators = []
        
        # IRR Risk
        project_irr = kpis.get('IRR_project', 0)
        if project_irr < 0.10:
            risk_indicators.append(("High Risk", "Project IRR below 10%", ft.Colors.RED))
        elif project_irr < 0.12:
            risk_indicators.append(("Medium Risk", "Project IRR below target 12%", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Project IRR above target", ft.Colors.GREEN))
        
        # DSCR Risk
        min_dscr = kpis.get('Min_DSCR', 0)
        if min_dscr < 1.20:
            risk_indicators.append(("High Risk", "DSCR below 1.20", ft.Colors.RED))
        elif min_dscr < 1.35:
            risk_indicators.append(("Medium Risk", "DSCR below comfortable level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Strong debt coverage", ft.Colors.GREEN))
        
        # LCOE Risk
        lcoe = kpis.get('LCOE_eur_kwh', 0)
        if lcoe > 0.055:
            risk_indicators.append(("High Risk", "LCOE above market", ft.Colors.RED))
        elif lcoe > 0.045:
            risk_indicators.append(("Medium Risk", "LCOE above competitive level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Competitive LCOE", ft.Colors.GREEN))
        
        risk_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.WARNING, color=indicator[2]),
                ft.Text(indicator[0], weight=ft.FontWeight.BOLD, color=indicator[2]),
                ft.Text(indicator[1], expand=True)
            ]) for indicator in risk_indicators
        ])
        
        return self.create_card(
            "Risk Assessment",
            risk_content,
            icon=ft.Icons.SECURITY,
            bgcolor=ft.Colors.ORANGE_50
        )
    
    def update_data(self, data: Dict[str, Any]):
        """Update dashboard with new financial results."""
        if "financial_results" in data:
            self.financial_results = data["financial_results"]
            self.refresh()
    
    def set_financial_results(self, results: Dict[str, Any]):
        """Set financial results for the dashboard."""
        self.financial_results = results
        
        # Enhanced debugging
        self.logger.info(f"Dashboard financial results updated. Results type: {type(results)}")
        if results:
            self.logger.info(f"Available keys: {list(results.keys())}")
            kpis = results.get('kpis', {})
            self.logger.info(f"KPIs available: {bool(kpis)}, KPI keys: {list(kpis.keys()) if kpis else 'None'}")
            
            # Log sample KPI values
            if kpis:
                sample_kpis = {k: v for k, v in list(kpis.items())[:5]}  # First 5 KPIs
                self.logger.info(f"Sample KPI values: {sample_kpis}")
            
            cashflow = results.get('cashflow')
            self.logger.info(f"Cashflow type: {type(cashflow)}, exists: {cashflow is not None}")
            
            if cashflow is not None:
                if hasattr(cashflow, 'columns'):
                    self.logger.info(f"Cashflow columns: {list(cashflow.columns)}")
                elif isinstance(cashflow, dict):
                    self.logger.info(f"Cashflow dict keys: {list(cashflow.keys())}")
        else:
            self.logger.warning("Dashboard received empty or None financial results!")
            
        self.refresh()

    def set_ml_predictions(self, predictions: Dict[str, Any]):
        """Set ML predictions for the dashboard."""
        self.ml_predictions = predictions
        self.ml_insights_widget = MLInsightsWidget(predictions)
        self.refresh()

    def set_3d_charts(self, charts_3d: Dict[str, str]):
        """Set 3D charts for the dashboard."""
        self.charts_3d = charts_3d
        self.refresh()

    def has_data(self) -> bool:
        """Check if dashboard has data to display."""
        return self.financial_results is not None

    def force_refresh_with_data(self, financial_results: Dict[str, Any], ml_predictions: Optional[Dict[str, Any]] = None, charts_3d: Optional[Dict[str, str]] = None):
        """Force refresh dashboard with all data at once."""
        self.financial_results = financial_results
        if ml_predictions:
            self.ml_predictions = ml_predictions
        if charts_3d:
            self.charts_3d = charts_3d

        self.logger.info(f"Dashboard force refresh - Financial: {bool(financial_results)}, ML: {bool(ml_predictions)}, 3D: {bool(charts_3d)}")
        self.refresh()

    def _create_3d_charts_section(self) -> ft.Card:
        """Create 3D charts section with interactive visualizations."""
        if not self.charts_3d:
            return ft.Container()

        # Create buttons for each 3D chart
        chart_buttons = []
        chart_info = {
            '3d_scenario_comparison': {
                'title': '3D Scenario Analysis',
                'description': 'Interactive 3D comparison of Base, Optimistic, and Pessimistic scenarios',
                'icon': ft.Icons.SCATTER_PLOT,
                'color': ft.Colors.BLUE
            },
            '3d_monte_carlo': {
                'title': '3D Monte Carlo Distribution',
                'description': 'Risk visualization with probability surfaces and confidence regions',
                'icon': ft.Icons.BUBBLE_CHART,
                'color': ft.Colors.GREEN
            },
            '3d_risk_analysis': {
                'title': '3D Risk Analysis',
                'description': 'Multi-dimensional risk factors visualization',
                'icon': ft.Icons.SECURITY,
                'color': ft.Colors.ORANGE
            }
        }

        for chart_key, chart_html in self.charts_3d.items():
            if chart_key in chart_info and chart_html and not chart_html.startswith('<!--'):
                info = chart_info[chart_key]
                chart_buttons.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(info['icon'], color=info['color'], size=30),
                                ft.Column([
                                    ft.Text(info['title'], size=16, weight=ft.FontWeight.BOLD),
                                    ft.Text(info['description'], size=12, color=ft.Colors.GREY_600)
                                ], expand=True),
                                ft.ElevatedButton(
                                    text="View 3D Chart",
                                    icon=ft.Icons.OPEN_IN_NEW,
                                    on_click=lambda e, key=chart_key: self._open_3d_chart(key),
                                    bgcolor=info['color'],
                                    color=ft.Colors.WHITE
                                )
                            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                        ]),
                        padding=15,
                        margin=5,
                        bgcolor=ft.Colors.with_opacity(0.05, info['color']),
                        border_radius=10,
                        border=ft.border.all(1, ft.Colors.with_opacity(0.2, info['color']))
                    )
                )

        if not chart_buttons:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.SCATTER_PLOT, color=ft.Colors.GREY_400, size=40),
                    ft.Text("3D Charts Available After Enhanced Analysis",
                           size=14, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER),
                    ft.Text("Run comprehensive analysis to generate interactive 3D visualizations",
                           size=12, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                padding=30,
                bgcolor=ft.Colors.GREY_50,
                border_radius=10,
                alignment=ft.alignment.center
            )

        return self.create_card(
            "📊 Interactive 3D Visualizations",
            ft.Column(chart_buttons, spacing=10),
            icon=ft.Icons.VIEW_IN_AR,
            bgcolor=ft.Colors.WHITE
        )

    def _open_3d_chart(self, chart_key: str):
        """Open 3D chart in browser."""
        if not self.charts_3d or chart_key not in self.charts_3d:
            return

        import tempfile
        import webbrowser
        import os

        try:
            # Create temporary HTML file
            chart_html = self.charts_3d[chart_key]

            # Create a temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                f.write(chart_html)
                temp_file_path = f.name

            # Open in default browser
            webbrowser.open(f'file://{temp_file_path}')

            # Show success message
            self.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"3D chart opened in browser"),
                bgcolor=ft.Colors.GREEN
            )
            self.page.snack_bar.open = True
            self.page.update()

        except Exception as e:
            # Show error message
            self.page.snack_bar = ft.SnackBar(
                content=ft.Text(f"Failed to open 3D chart: {str(e)}"),
                bgcolor=ft.Colors.RED
            )
            self.page.snack_bar.open = True
            self.page.update()

    def _create_enhanced_charts_grid(self) -> ft.Container:
        """Create enhanced charts grid with trend analysis."""
        if not self.financial_results:
            return ft.Container()

        # Create enhanced chart components
        kpi_gauge_chart = self.kpi_charts.create_kpi_gauge_chart(self.financial_results)
        irr_comparison_chart = self.kpi_charts.create_irr_comparison_chart(self.financial_results)
        cashflow_timeline = self.cashflow_charts.create_cashflow_timeline_chart(self.financial_results)
        dscr_timeline = self.cashflow_charts.create_dscr_timeline_chart(self.financial_results)

        charts_grid = ft.Column([
            ft.Row([
                ft.Container(content=kpi_gauge_chart, expand=1),
                ft.Container(content=irr_comparison_chart, expand=1)
            ]),
            ft.Container(height=10),
            ft.Row([
                ft.Container(content=cashflow_timeline, expand=1),
                ft.Container(content=dscr_timeline, expand=1)
            ])
        ])

        return self.create_card(
            "Enhanced Financial Analysis Charts • 2025",
            charts_grid,
            icon=ft.Icons.ANALYTICS
        )

    def _create_dcf_analysis(self) -> ft.Card:
        """Create DCF analysis section."""
        if not self.financial_results:
            return ft.Card()

        kpis = self.financial_results.get('kpis', {})
        dcf_assumptions = self.financial_results.get('dcf_assumptions', {})

        dcf_content = ft.Column([
            ft.Row([
                ft.Container(
                    content=ft.Column([
                        ft.Text("DCF Methodology", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                        ft.Text("Terminal Value Method", size=12, color=ft.Colors.GREY_600),
                        ft.Text(dcf_assumptions.get('terminal_value_method', 'Perpetuity').title(),
                               size=14, weight=ft.FontWeight.W_500),
                        ft.Text("Growth Rate", size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"{dcf_assumptions.get('terminal_growth_rate', 0.025):.1%}",
                               size=14, weight=ft.FontWeight.W_500),
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8
                ),
                ft.Container(width=10),
                ft.Container(
                    content=ft.Column([
                        ft.Text("Valuation Metrics", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                        ft.Text("NPV Project", size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"€{kpis.get('NPV_project', 0)/1e6:.1f}M",
                               size=14, weight=ft.FontWeight.W_500),
                        ft.Text("NPV Equity", size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"€{kpis.get('NPV_equity', 0)/1e6:.1f}M",
                               size=14, weight=ft.FontWeight.W_500),
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8
                ),
                ft.Container(width=10),
                ft.Container(
                    content=ft.Column([
                        ft.Text("Risk Metrics", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
                        ft.Text("Debt-to-Equity", size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"{kpis.get('Debt_to_equity_ratio', 0):.2f}",
                               size=14, weight=ft.FontWeight.W_500),
                        ft.Text("Min DSCR", size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"{kpis.get('Min_DSCR', 0):.2f}",
                               size=14, weight=ft.FontWeight.W_500),
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.ORANGE_50,
                    border_radius=8
                )
            ])
        ])

        return self.create_card(
            "DCF Analysis • 2025 Standards",
            dcf_content,
            icon=ft.Icons.CALCULATE,
            bgcolor=ft.Colors.WHITE
        )

    def _create_enhanced_risk_analysis(self) -> ft.Card:
        """Create enhanced risk analysis with 2025 standards."""
        if not self.financial_results:
            return ft.Card()

        kpis = self.financial_results.get('kpis', {})

        # Enhanced risk indicators with 2025 benchmarks
        risk_indicators = []

        # IRR Risk (updated thresholds)
        project_irr = kpis.get('IRR_project', 0)
        if project_irr < 0.08:
            risk_indicators.append(("High Risk", "Project IRR below 8% (2025 minimum)", ft.Colors.RED))
        elif project_irr < 0.11:
            risk_indicators.append(("Medium Risk", "Project IRR below 11% target", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Project IRR above 2025 targets", ft.Colors.GREEN))

        # DSCR Risk
        min_dscr = kpis.get('Min_DSCR', 0)
        if min_dscr < 1.20:
            risk_indicators.append(("High Risk", "DSCR below 1.20 (lender minimum)", ft.Colors.RED))
        elif min_dscr < 1.35:
            risk_indicators.append(("Medium Risk", "DSCR below comfortable 1.35", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Strong debt coverage ratio", ft.Colors.GREEN))

        # LCOE Risk (2025 benchmarks)
        lcoe = kpis.get('LCOE_eur_kwh', 0)
        if lcoe > 0.060:
            risk_indicators.append(("High Risk", "LCOE above 2025 market rates", ft.Colors.RED))
        elif lcoe > 0.045:
            risk_indicators.append(("Medium Risk", "LCOE above competitive level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Highly competitive LCOE", ft.Colors.GREEN))

        # Technology Risk
        if self.reliability_metrics:
            tech_risk = self.reliability_metrics.technology_risk_factor
            if tech_risk > 0.4:
                risk_indicators.append(("High Risk", "Significant technology uncertainties", ft.Colors.RED))
            elif tech_risk > 0.2:
                risk_indicators.append(("Medium Risk", "Moderate technology risk", ft.Colors.ORANGE))
            else:
                risk_indicators.append(("Low Risk", "Proven technology solution", ft.Colors.GREEN))

        risk_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.WARNING, color=indicator[2], size=20),
                ft.Container(
                    content=ft.Column([
                        ft.Text(indicator[0], weight=ft.FontWeight.BOLD, color=indicator[2], size=14),
                        ft.Text(indicator[1], color=ft.Colors.GREY_700, size=12)
                    ], spacing=2),
                    expand=True
                )
            ], spacing=10) for indicator in risk_indicators
        ], spacing=10)

        return self.create_card(
            "Enhanced Risk Assessment • 2025 Standards",
            risk_content,
            icon=ft.Icons.SECURITY,
            bgcolor=ft.Colors.ORANGE_50
        )

    def _create_factor_analysis(self) -> ft.Card:
        """Create factor analysis and trends section."""
        if not self.financial_results:
            return ft.Card()

        kpis = self.financial_results.get('kpis', {})
        assumptions = self.financial_results.get('assumptions', {})

        # Key factors analysis
        factors_content = ft.Column([
            ft.Text("Key Performance Factors • 2025 Analysis",
                   size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
            ft.Container(height=10),

            ft.Row([
                ft.Container(
                    content=ft.Column([
                        ft.Text("Technology Factors", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                        ft.Text(f"• Capacity Factor: {(assumptions.get('production_mwh_year1', 18000) / (assumptions.get('capacity_mw', 10) * 8760)):.1%}", size=12),
                        ft.Text(f"• Degradation Rate: {assumptions.get('degradation_rate', 0.005):.2%}/year", size=12),
                        ft.Text(f"• CAPEX: €{assumptions.get('capex_meur', 8.5)/assumptions.get('capacity_mw', 10)*1000:.0f}/kW", size=12),
                        ft.Text("• Technology: Bifacial + Tracking", size=12, color=ft.Colors.GREEN_600)
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8
                ),
                ft.Container(width=10),
                ft.Container(
                    content=ft.Column([
                        ft.Text("Market Factors", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                        ft.Text(f"• PPA Price: €{assumptions.get('ppa_price_eur_kwh', 0.045):.3f}/kWh", size=12),
                        ft.Text(f"• Discount Rate: {assumptions.get('discount_rate', 0.08):.1%}", size=12),
                        ft.Text(f"• Debt Ratio: {assumptions.get('debt_ratio', 0.75):.0%}", size=12),
                        ft.Text("• Market: Competitive auctions", size=12, color=ft.Colors.BLUE_600)
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8
                ),
                ft.Container(width=10),
                ft.Container(
                    content=ft.Column([
                        ft.Text("2025 Trends", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_700),
                        ft.Text("• Cost reduction: -15% vs 2020", size=12),
                        ft.Text("• Efficiency gains: +20%", size=12),
                        ft.Text("• ESG compliance: Mandatory", size=12),
                        ft.Text("• Grid parity: Achieved", size=12, color=ft.Colors.PURPLE_600)
                    ]),
                    expand=1,
                    padding=15,
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=8
                )
            ])
        ])

        return self.create_card(
            "Factor Analysis & Market Trends",
            factors_content,
            icon=ft.Icons.TRENDING_UP,
            bgcolor=ft.Colors.WHITE
        )

    def _create_ml_insights_section(self) -> ft.Container:
        """Create ML insights section with real data."""
        if not self.financial_results or not self.integration_service:
            return ft.Container()

        try:
            # Get ML predictions if available
            assumptions = self.financial_results.get('assumptions', {})
            ml_predictions = {}
            
            if hasattr(self.integration_service, 'ml_service') and self.integration_service.ml_service:
                # Get predictions for key metrics
                predictions = self.integration_service.ml_service.predict_multiple(assumptions)
                
                ml_insights = {
                    "predictions": {
                        target.value: {
                            "predicted": result.predicted_value,
                            "confidence_interval": result.confidence_interval
                        }
                        for target, result in predictions.items()
                    },
                    "recommendations": [
                        rec for result in predictions.values() 
                        for rec in result.recommendations[:2]
                    ][:5],  # Top 5 recommendations
                    "confidence_score": 0.85  # Average confidence
                }
                
                # Update ML insights widget
                self.ml_insights_widget.insights = ml_insights
                return self.ml_insights_widget.build()
            else:
                # Fallback if ML not available
                return ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.Icons.PSYCHOLOGY, color=ft.Colors.PURPLE_200, size=40),
                        ft.Text("ML Service Initializing...", 
                               size=14, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER),
                        ft.Text("Enable ML predictions in the features panel above", 
                               size=12, color=ft.Colors.GREY_400, text_align=ft.TextAlign.CENTER)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
                    padding=40,
                    bgcolor=ft.Colors.PURPLE_50,
                    border_radius=10,
                    alignment=ft.alignment.center
                )
                
        except Exception as e:
            self.logger.error(f"Error creating ML insights: {e}")
            return ft.Container()

    def _update_features_status(self):
        """Update features status based on integration service."""
        try:
            if self.integration_service:
                # Get system status
                status = self.integration_service.get_system_status()
                
                # Extract features status
                features_status = status.get('integration_service', {}).get('features_enabled', {})
                
                # Extract system stats
                system_stats = {
                    'cache_hits': status.get('cache_service', {}).get('hits', 0),
                    'total_operations': status.get('integration_service', {}).get('total_operations', 0),
                    'saved_versions': len(self.integration_service.persistence_service.list_projects(limit=100)) if self.integration_service.persistence_service else 0,
                    'ml_predictions': status.get('ml_service', {}).get('total_predictions', 0)
                }
                
                # Update the panel
                self.enhanced_features_panel.update_status(features_status, system_stats)
                
        except Exception as e:
            self.logger.error(f"Error updating features status: {e}")

    def _handle_feature_toggle(self, feature_id: str, enabled: bool):
        """Handle feature toggle."""
        try:
            self.logger.info(f"Toggling feature {feature_id}: {enabled}")
            
            # Update integration service feature flags
            if self.integration_service:
                self.integration_service.features[feature_id] = enabled
                
                # Reinitialize specific services if needed
                if feature_id == "ml" and enabled:
                    # Initialize ML service if not already initialized
                    if not self.integration_service.ml_service:
                        from services.ml_prediction_service import MLPredictionService
                        self.integration_service.ml_service = MLPredictionService()
                        self.logger.info("ML service initialized")
                
                # Show notification
                self.show_notification(
                    f"{feature_id.replace('_', ' ').title()} {'enabled' if enabled else 'disabled'}",
                    ft.Colors.GREEN if enabled else ft.Colors.ORANGE
                )
                
                # Update status display
                self._update_features_status()
                
                # Refresh the view
                self.refresh()
                
        except Exception as e:
            self.logger.error(f"Error toggling feature {feature_id}: {e}")
            self.show_notification(f"Failed to toggle {feature_id}: {str(e)}", ft.Colors.RED)

    def _handle_feature_action(self, action: str):
        """Handle feature action."""
        try:
            self.logger.info(f"Handling feature action: {action}")
            
            if action == "clear_cache":
                if self.integration_service and self.integration_service.cache_service:
                    self.integration_service.cache_service.clear_all()
                    self.show_notification("Cache cleared successfully", ft.Colors.GREEN)
                    
            elif action == "backup_now":
                if self.integration_service and self.integration_service.persistence_service:
                    success = self.integration_service.persistence_service.create_backup()
                    if success:
                        self.show_notification("Backup created successfully", ft.Colors.GREEN)
                    else:
                        self.show_notification("Backup failed", ft.Colors.RED)
                        
            elif action == "view_history":
                if self.integration_service and self.integration_service.undo_redo_service:
                    history = self.integration_service.undo_redo_service.get_history_summary(limit=10)
                    # TODO: Show history in a dialog
                    self.show_notification(f"History: {len(history)} operations", ft.Colors.BLUE)
                    
            elif action == "ml_report":
                if self.integration_service and self.integration_service.ml_service:
                    stats = self.integration_service.ml_service.get_model_statistics()
                    # TODO: Show ML report in a dialog
                    self.show_notification(f"ML Models trained: {stats.get('models_trained', 0)}", ft.Colors.PURPLE)
                    
            elif action == "configure":
                # TODO: Show configuration dialog
                self.show_notification("Configuration dialog coming soon", ft.Colors.BLUE)
                
            # Update status after action
            self._update_features_status()
            
        except Exception as e:
            self.logger.error(f"Error handling action {action}: {e}")
            self.show_notification(f"Action failed: {str(e)}", ft.Colors.RED)
    
    def _create_enhanced_empty_state(self) -> ft.Control:
        """Create enhanced empty state with skeleton loading and better UX."""
        accessibility_manager = get_accessibility_manager()
        
        # Create skeleton loading for dashboard components
        skeleton_content = ft.Column([
            # Header skeleton
            create_skeleton(SkeletonType.TEXT, count=1, custom_dimensions={'width': 400, 'height': 24}),
            
            ft.Container(height=20),
            
            # KPI cards skeleton
            ft.Row([
                create_skeleton(SkeletonType.CARD, count=1, custom_dimensions={'width': 180, 'height': 120})
                for _ in range(6)
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
            
            ft.Container(height=20),
            
            # Charts skeleton
            ft.Row([
                create_skeleton(SkeletonType.CHART, count=1, custom_dimensions={'width': 400, 'height': 250}),
                create_skeleton(SkeletonType.CHART, count=1, custom_dimensions={'width': 400, 'height': 250})
            ], spacing=20),
            
            ft.Container(height=20),
            
            # Analysis sections skeleton
            create_skeleton(SkeletonType.CARD, count=3, custom_dimensions={'width': 800, 'height': 150})
        ], spacing=15)
        
        # Enhanced call-to-action with accessibility
        cta_button = make_accessible_button(
            text="🚀 Start Financial Analysis",
            on_click=lambda e: self.navigate_to("project_setup"),
            aria_label="Navigate to project setup to start financial analysis",
            aria_description="Begin by setting up your project parameters and assumptions",
            keyboard_shortcut="Ctrl+S"
        )
        
        # Add semantic markup for screen readers
        main_heading = ft.Text(
            "Welcome to Enhanced Financial Analytics Dashboard",
            size=28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_700,
            text_align=ft.TextAlign.CENTER
        )
        ScreenReaderSupport.mark_as_heading(main_heading, 1, "Welcome to Enhanced Financial Analytics Dashboard")
        
        subtitle = ft.Text(
            "Comprehensive DCF modeling with ML predictions and 3D visualizations",
            size=16,
            color=ft.Colors.GREY_600,
            text_align=ft.TextAlign.CENTER
        )
        
        # Features preview with accessibility
        features_preview = ft.Column([
            ft.Text("✨ What you'll get:", size=18, weight=ft.FontWeight.W_600, color=ft.Colors.BLUE_600),
            ft.Column([
                self._create_feature_item("📊", "Interactive KPI Dashboard", "Real-time financial metrics and performance indicators"),
                self._create_feature_item("🤖", "ML-Powered Predictions", "Advanced forecasting with confidence intervals"),
                self._create_feature_item("🎯", "3D Risk Analysis", "Multi-dimensional risk visualization"),
                self._create_feature_item("📈", "DCF Modeling", "Professional-grade discounted cash flow analysis"),
                self._create_feature_item("🔄", "Real-time Updates", "Live data synchronization and auto-save"),
            ], spacing=8)
        ], spacing=12)
        
        # Accessibility announcement
        accessibility_manager.announce_to_screen_reader(
            "Dashboard is ready. Please run financial analysis to view results.",
            "polite"
        )
        
        return ft.Container(
            content=ft.Column([
                ft.Container(height=40),
                main_heading,
                subtitle,
                ft.Container(height=30),
                features_preview,
                ft.Container(height=30),
                cta_button,
                ft.Container(height=40),
                ft.Divider(color=ft.Colors.GREY_300),
                ft.Container(height=20),
                ft.Text("Preview: Loading Dashboard Layout", 
                       size=14, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER),
                ft.Container(height=10),
                skeleton_content
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=40,
            expand=True
        )
    
    def _create_feature_item(self, icon: str, title: str, description: str) -> ft.Control:
        """Create an accessible feature preview item."""
        feature_row = ft.Row([
            ft.Text(icon, size=20),
            ft.Column([
                ft.Text(title, size=14, weight=ft.FontWeight.W_500, color=ft.Colors.GREY_800),
                ft.Text(description, size=12, color=ft.Colors.GREY_600)
            ], spacing=2, expand=True)
        ], spacing=12, alignment=ft.MainAxisAlignment.START)
        
        # Add accessibility support
        ScreenReaderSupport.add_aria_label(feature_row, f"{title}: {description}")
        
        return feature_row

    def _handle_scroll_event(self, e: ft.ControlEvent):
        """Handle scroll events for smooth scrolling experience."""
        try:
            # Log scroll position for debugging if needed
            if hasattr(e, 'pixels') and e.pixels is not None:
                scroll_percentage = e.pixels / e.max_scroll_extent if e.max_scroll_extent > 0 else 0
                
                # Show/hide back-to-top button based on scroll position
                if scroll_percentage > 0.3:  # Show when scrolled down 30%
                    self._show_back_to_top_button()
                else:
                    self._hide_back_to_top_button()
                    
        except Exception as scroll_error:
            # Scroll events should not break the app
            self.logger.debug(f"Scroll event handling error: {scroll_error}")

    def _show_back_to_top_button(self):
        """Show back to top button (future enhancement)."""
        # TODO: Implement floating action button for scroll to top
        pass

    def _hide_back_to_top_button(self):
        """Hide back to top button (future enhancement)."""
        # TODO: Hide floating action button
        pass
