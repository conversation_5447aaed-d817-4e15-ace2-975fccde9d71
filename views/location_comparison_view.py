"""
Location Comparison View
========================

View component for comparing different project locations.
"""

import flet as ft
from typing import Dict, Any, Optional, List

from .base_view import BaseView
from components.charts.comparison_charts import ComparisonCharts


class LocationComparisonView(BaseView):
    """View for location comparison and analysis."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.comparison_results: Optional[Dict[str, Any]] = None
        self.selected_locations: List[str] = ["Ouarzazate", "Dakhla"]
        self.available_locations = [
            "Ouarzazate", "Dakh<PERSON>", "Tarfaya", "Noor Midelt", 
            "Laâyoune", "Tan-Tan", "Boujdour", "Tata", "Zagora"
        ]
        self.comparison_charts = ComparisonCharts()
    
    def build_content(self) -> ft.Control:
        """Build the location comparison view content."""
        
        # Header
        header = self.create_section_header(
            "Location Comparison",
            "Compare different locations for optimal project placement"
        )
        
        # Location selection
        location_selection = self._create_location_selection()
        
        # Comparison results
        if self.comparison_results:
            comparison_content = self._create_comparison_results()
        else:
            comparison_content = self.create_empty_state(
                "No Comparison Results",
                "Select locations and run comparison to see results",
                "Run Comparison",
                self._on_run_comparison
            )
        
        return ft.Column([
            header,
            location_selection,
            comparison_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_location_selection(self) -> ft.Card:
        """Create location selection interface."""
        
        # Available locations checkboxes
        location_checkboxes = []
        for location in self.available_locations:
            checkbox = ft.Checkbox(
                label=location,
                value=location in self.selected_locations,
                on_change=lambda e, loc=location: self._on_location_selected(loc, e.control.value)
            )
            location_checkboxes.append(checkbox)
        
        # Organize in rows of 3
        checkbox_rows = []
        for i in range(0, len(location_checkboxes), 3):
            row_checkboxes = location_checkboxes[i:i+3]
            checkbox_rows.append(ft.Row(row_checkboxes))
        
        selection_content = ft.Column([
            ft.Text("Select Locations to Compare:", size=16, weight=ft.FontWeight.BOLD),
            ft.Text(f"Currently selected: {len(self.selected_locations)} locations", 
                   size=12, color=ft.Colors.GREY_600),
            ft.Divider(height=10),
            *checkbox_rows,
            ft.Divider(height=20),
            ft.Row([
                self.create_action_button(
                    "Select All",
                    ft.Icons.SELECT_ALL,
                    self._on_select_all,
                    ft.Colors.BLUE_600
                ),
                self.create_action_button(
                    "Clear All",
                    ft.Icons.CLEAR_ALL,
                    self._on_clear_all,
                    ft.Colors.GREY_600
                ),
                self.create_action_button(
                    "Run Comparison",
                    ft.Icons.COMPARE_ARROWS,
                    self._on_run_comparison,
                    ft.Colors.GREEN_600
                )
            ], alignment=ft.MainAxisAlignment.CENTER)
        ])
        
        return self.create_card(
            "Location Selection",
            selection_content,
            icon=ft.Icons.LOCATION_ON
        )
    
    def _create_comparison_results(self) -> ft.Column:
        """Create comparison results display."""
        if not self.comparison_results:
            return ft.Column()
        
        analysis = self.comparison_results.get('analysis', {})
        locations = self.comparison_results.get('locations', {})
        
        # Summary table
        summary_table = self._create_summary_table()
        
        # Rankings
        rankings = self._create_rankings()
        
        # Recommendations
        recommendations = self._create_recommendations()
        
        # Charts
        charts = self._create_comparison_charts()
        
        return ft.Column([
            summary_table,
            rankings,
            recommendations,
            charts
        ])
    
    def _create_summary_table(self) -> ft.Card:
        """Create comparison summary table."""
        analysis = self.comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return ft.Card()
        
        # Create table headers
        headers = [
            ft.DataColumn(ft.Text("Location")),
            ft.DataColumn(ft.Text("IRR Project")),
            ft.DataColumn(ft.Text("IRR Equity")),
            ft.DataColumn(ft.Text("NPV (M€)")),
            ft.DataColumn(ft.Text("LCOE (€/kWh)")),
            ft.DataColumn(ft.Text("Min DSCR")),
            ft.DataColumn(ft.Text("Cap. Factor"))
        ]
        
        # Create table rows
        rows = []
        for location_data in comparison_matrix:
            row = ft.DataRow(cells=[
                ft.DataCell(ft.Text(location_data['Location'])),
                ft.DataCell(ft.Text(f"{location_data['IRR_Project']:.1%}")),
                ft.DataCell(ft.Text(f"{location_data['IRR_Equity']:.1%}")),
                ft.DataCell(ft.Text(f"{location_data['NPV_Project_MEUR']:.1f}")),
                ft.DataCell(ft.Text(f"{location_data['LCOE_EUR_kWh']:.3f}")),
                ft.DataCell(ft.Text(f"{location_data['Min_DSCR']:.2f}")),
                ft.DataCell(ft.Text(f"{location_data['Capacity_Factor']:.1%}"))
            ])
            rows.append(row)
        
        table = ft.DataTable(
            columns=headers,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            vertical_lines=ft.border.BorderSide(1, ft.Colors.GREY_300),
            horizontal_lines=ft.border.BorderSide(1, ft.Colors.GREY_300)
        )
        
        return self.create_card(
            "Location Comparison Summary",
            ft.Container(content=table, padding=10),
            icon=ft.Icons.TABLE_CHART
        )
    
    def _create_rankings(self) -> ft.Card:
        """Create rankings display."""
        analysis = self.comparison_results.get('analysis', {})
        rankings = analysis.get('rankings', {})
        
        ranking_content = ft.Column([
            ft.Row([
                self._create_ranking_column("Best Project IRR", rankings.get('best_irr_project', [])),
                self._create_ranking_column("Best Equity IRR", rankings.get('best_irr_equity', [])),
                self._create_ranking_column("Lowest LCOE", rankings.get('lowest_lcoe', []))
            ])
        ])
        
        return self.create_card(
            "Location Rankings",
            ranking_content,
            icon=ft.Icons.LEADERBOARD
        )
    
    def _create_ranking_column(self, title: str, ranking_data: List[Dict]) -> ft.Container:
        """Create a ranking column."""
        ranking_items = []
        
        for item in ranking_data[:5]:  # Top 5
            rank_color = ft.Colors.AMBER if item['rank'] == 1 else ft.Colors.BLUE_GREY_400 if item['rank'] == 2 else ft.Colors.BROWN_700 if item['rank'] == 3 else ft.Colors.GREY
            
            ranking_items.append(
                ft.Row([
                    ft.Container(
                        content=ft.Text(str(item['rank']), color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        width=30,
                        height=30,
                        bgcolor=rank_color,
                        border_radius=15,
                        alignment=ft.alignment.center
                    ),
                    ft.Text(item['location'], expand=True),
                    ft.Text(f"{item['value']:.3f}" if isinstance(item['value'], float) else str(item['value']))
                ])
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=14, weight=ft.FontWeight.BOLD),
                ft.Divider(height=5),
                *ranking_items
            ]),
            padding=10,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            expand=True
        )
    
    def _create_recommendations(self) -> ft.Card:
        """Create recommendations display."""
        analysis = self.comparison_results.get('analysis', {})
        recommendations = analysis.get('recommendations', {})
        
        if not recommendations:
            return ft.Card()
        
        rec_content = ft.Column([
            ft.Text("Recommendations", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10)
        ])
        
        # Best overall location
        if 'best_overall' in recommendations:
            best_overall = recommendations['best_overall']
            rec_content.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER),
                            ft.Text("Best Overall Location", size=16, weight=ft.FontWeight.BOLD)
                        ]),
                        ft.Text(f"Location: {best_overall['location']}", size=14),
                        ft.Text(f"Score: {best_overall['score']:.3f}", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Strengths:", size=12, weight=ft.FontWeight.BOLD),
                        *[ft.Text(f"• {reason}", size=12) for reason in best_overall.get('reasons', [])]
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.AMBER)
                )
            )
        
        # Specific recommendations
        specific_recs = [
            ("Best for Returns", recommendations.get('best_for_returns')),
            ("Best for LCOE", recommendations.get('best_for_lcoe')),
            ("Best for Risk Management", recommendations.get('best_for_risk'))
        ]
        
        for title, location in specific_recs:
            if location:
                rec_content.controls.append(
                    self.create_info_row(title, location, ft.Colors.BLUE_700)
                )
        
        return self.create_card(
            "Analysis Recommendations",
            rec_content,
            icon=ft.Icons.RECOMMEND,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_comparison_charts(self) -> ft.Card:
        """Create comparison charts."""
        if not self.comparison_results:
            return ft.Card()
        
        # This would use the ComparisonCharts component
        chart_placeholder = ft.Container(
            content=ft.Text("Comparison charts would be displayed here", 
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Comparison Charts",
            chart_placeholder,
            icon=ft.Icons.BAR_CHART
        )
    
    def _on_location_selected(self, location: str, selected: bool):
        """Handle location selection change."""
        if selected and location not in self.selected_locations:
            self.selected_locations.append(location)
        elif not selected and location in self.selected_locations:
            self.selected_locations.remove(location)
        
        self.refresh()
    
    def _on_select_all(self, e):
        """Select all locations."""
        self.selected_locations = self.available_locations.copy()
        self.refresh()
    
    def _on_clear_all(self, e):
        """Clear all location selections."""
        self.selected_locations = []
        self.refresh()
    
    def _on_run_comparison(self, e=None):
        """Run location comparison."""
        if len(self.selected_locations) < 2:
            self.show_error("Please select at least 2 locations for comparison")
            return
        
        self.request_action("run_location_comparison", {
            "locations": self.selected_locations
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with comparison results."""
        if "comparison_results" in data:
            self.comparison_results = data["comparison_results"]
            self.refresh()
        
        if "selected_locations" in data:
            self.selected_locations = data["selected_locations"]
            self.refresh()
    
    def set_comparison_results(self, results: Dict[str, Any]):
        """Set comparison results."""
        self.comparison_results = results
        self.refresh()
    
    def get_selected_locations(self) -> List[str]:
        """Get currently selected locations."""
        return self.selected_locations.copy()
    
    def set_selected_locations(self, locations: List[str]):
        """Set selected locations."""
        self.selected_locations = [loc for loc in locations if loc in self.available_locations]
        self.refresh()
