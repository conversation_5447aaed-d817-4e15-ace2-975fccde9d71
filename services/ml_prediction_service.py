"""
ML Prediction Service
====================

Machine learning predictions for financial modeling with risk assessment and optimization.
"""

import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass
from enum import Enum

try:
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class PredictionTarget(Enum):
    """Prediction targets."""
    IRR_EQUITY = "irr_equity"
    IRR_PROJECT = "irr_project"
    NPV_EQUITY = "npv_equity"
    LCOE = "lcoe"
    RISK_SCORE = "risk_score"


@dataclass
class PredictionResult:
    """ML prediction result."""
    target: PredictionTarget
    predicted_value: float
    confidence_interval: Tuple[float, float]
    feature_contributions: Dict[str, float]
    model_confidence: float
    recommendations: List[str]


class FinancialDataGenerator:
    """Generate synthetic financial data for training."""
    
    def generate_training_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """Generate synthetic training data based on realistic ranges."""
        np.random.seed(42)
        
        data = {
            'capacity_mw': np.random.uniform(5, 100, n_samples),
            'capex_eur_kw': np.random.uniform(800, 1500, n_samples),
            'capacity_factor': np.random.uniform(0.15, 0.35, n_samples),
            'ppa_price_eur_kwh': np.random.uniform(0.03, 0.08, n_samples),
            'debt_ratio': np.random.uniform(0.6, 0.85, n_samples),
            'interest_rate': np.random.uniform(0.04, 0.08, n_samples),
            'discount_rate': np.random.uniform(0.06, 0.12, n_samples),
        }
        
        df = pd.DataFrame(data)
        
        # Calculate synthetic targets
        df['irr_equity'] = self._calculate_synthetic_irr(df)
        df['npv_equity'] = self._calculate_synthetic_npv(df)
        df['lcoe'] = self._calculate_synthetic_lcoe(df)
        
        return df
    
    def _calculate_synthetic_irr(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic IRR."""
        base_irr = 0.08 + (df['ppa_price_eur_kwh'] - 0.05) * 2
        base_irr += df['capacity_factor'] * 0.2
        base_irr -= df['debt_ratio'] * 0.05
        
        noise = np.random.normal(0, 0.01, len(df))
        return np.clip(base_irr + noise, 0.05, 0.25)
    
    def _calculate_synthetic_npv(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic NPV."""
        revenue_factor = df['capacity_mw'] * df['capacity_factor'] * df['ppa_price_eur_kwh']
        cost_factor = df['capacity_mw'] * df['capex_eur_kw'] / 1000
        
        npv = revenue_factor * 100 - cost_factor
        noise = np.random.normal(0, npv.std() * 0.1, len(df))
        
        return npv + noise
    
    def _calculate_synthetic_lcoe(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate synthetic LCOE."""
        lcoe = df['capex_eur_kw'] / (df['capacity_factor'] * 8760 * 25)
        lcoe += 0.01  # Add base O&M
        
        noise = np.random.normal(0, 0.002, len(df))
        return np.clip(lcoe + noise, 0.02, 0.15)


class MLPredictionService:
    """ML prediction service for financial modeling."""
    
    def __init__(self, models_dir: str = "ml_models"):
        if not SKLEARN_AVAILABLE:
            self.logger = logging.getLogger(__name__)
            self.logger.warning("scikit-learn not available - ML predictions disabled")
            return
        
        self.logger = logging.getLogger(__name__)
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.models: Dict[PredictionTarget, Any] = {}
        self.scalers: Dict[PredictionTarget, StandardScaler] = {}
        self.feature_columns = [
            'capacity_mw', 'capex_eur_kw', 'capacity_factor',
            'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate'
        ]
        
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize models with synthetic data."""
        if not SKLEARN_AVAILABLE:
            return
        
        try:
            generator = FinancialDataGenerator()
            training_data = generator.generate_training_data(1000)
            
            targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
            
            for target in targets:
                self._train_model(training_data, target)
            
            self.logger.info("ML models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize ML models: {e}")
    
    def _train_model(self, data: pd.DataFrame, target: PredictionTarget):
        """Train model for specific target."""
        X = data[self.feature_columns]
        y = data[target.value]
        
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X_train_scaled, y_train)
        
        y_pred = model.predict(X_test_scaled)
        r2 = r2_score(y_test, y_pred)
        
        self.models[target] = model
        self.scalers[target] = scaler
        
        self.logger.info(f"Trained {target.value} model - R²: {r2:.3f}")
    
    def predict(self, assumptions: Dict[str, Any], target: PredictionTarget) -> PredictionResult:
        """Make prediction for given assumptions."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            # Return fallback prediction
            return PredictionResult(
                target=target,
                predicted_value=0.12 if target == PredictionTarget.IRR_EQUITY else 1000000,
                confidence_interval=(0.10, 0.14),
                feature_contributions={},
                model_confidence=0.5,
                recommendations=["ML models not available - using fallback values"]
            )
        
        input_data = self._prepare_input_data(assumptions)
        model = self.models[target]
        scaler = self.scalers[target]
        
        input_scaled = scaler.transform([input_data])
        prediction = model.predict(input_scaled)[0]
        
        # Simple confidence interval
        confidence_interval = (prediction * 0.9, prediction * 1.1)
        
        # Feature contributions
        if hasattr(model, 'feature_importances_'):
            contributions = dict(zip(self.feature_columns, model.feature_importances_))
        else:
            contributions = {}
        
        recommendations = self._generate_recommendations(target, prediction)
        
        return PredictionResult(
            target=target,
            predicted_value=prediction,
            confidence_interval=confidence_interval,
            feature_contributions=contributions,
            model_confidence=0.8,
            recommendations=recommendations
        )
    
    def _prepare_input_data(self, assumptions: Dict[str, Any]) -> List[float]:
        """Prepare input data for prediction."""
        defaults = {
            'capacity_mw': 10.0,
            'capex_eur_kw': 1000.0,
            'capacity_factor': 0.25,
            'ppa_price_eur_kwh': 0.05,
            'debt_ratio': 0.75,
            'interest_rate': 0.06,
            'discount_rate': 0.08
        }
        
        return [assumptions.get(feature, defaults[feature]) for feature in self.feature_columns]
    
    def _generate_recommendations(self, target: PredictionTarget, prediction: float) -> List[str]:
        """Generate recommendations based on prediction."""
        recommendations = []
        
        if target == PredictionTarget.IRR_EQUITY:
            if prediction < 0.10:
                recommendations.append("Consider increasing PPA price or reducing CAPEX")
            elif prediction > 0.20:
                recommendations.append("Excellent returns - verify assumptions")
        
        return recommendations


    def predict_multiple(self, assumptions: Dict[str, Any], 
                        targets: List[PredictionTarget] = None) -> Dict[PredictionTarget, PredictionResult]:
        """Make predictions for multiple targets."""
        if targets is None:
            targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
        
        results = {}
        for target in targets:
            try:
                results[target] = self.predict(assumptions, target)
            except Exception as e:
                self.logger.error(f"Failed to predict {target.value}: {e}")
                # Add fallback result
                results[target] = self._get_fallback_prediction(target)
        
        return results
    
    def optimize_parameters(self, base_assumptions: Dict[str, Any], 
                           target: PredictionTarget, target_value: float,
                           variable_params: List[str] = None) -> Dict[str, Any]:
        """Optimize parameters to achieve target value."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            return base_assumptions.copy()
        
        if variable_params is None:
            variable_params = ['capacity_mw', 'ppa_price_eur_kwh', 'capex_eur_kw']
        
        best_assumptions = base_assumptions.copy()
        best_diff = float('inf')
        
        # Simple grid search optimization
        for param in variable_params:
            original_value = base_assumptions.get(param, 0)
            
            # Try different values around the original
            test_values = self._get_parameter_range(param, original_value)
            
            for test_value in test_values:
                test_assumptions = base_assumptions.copy()
                test_assumptions[param] = test_value
                
                try:
                    prediction = self.predict(test_assumptions, target)
                    diff = abs(prediction.predicted_value - target_value)
                    
                    if diff < best_diff:
                        best_diff = diff
                        best_assumptions = test_assumptions.copy()
                        
                except Exception:
                    continue
        
        return best_assumptions
    
    def sensitivity_analysis(self, base_assumptions: Dict[str, Any], 
                           target: PredictionTarget, variation_pct: float = 0.1) -> Dict[str, float]:
        """Perform sensitivity analysis on prediction."""
        if not SKLEARN_AVAILABLE or target not in self.models:
            return {}
        
        base_prediction = self.predict(base_assumptions, target)
        sensitivities = {}
        
        for param in self.feature_columns:
            if param in base_assumptions:
                original_value = base_assumptions[param]
                
                # Test positive variation
                test_assumptions = base_assumptions.copy()
                test_assumptions[param] = original_value * (1 + variation_pct)
                
                try:
                    high_prediction = self.predict(test_assumptions, target)
                    sensitivity = (high_prediction.predicted_value - base_prediction.predicted_value) / (original_value * variation_pct)
                    sensitivities[param] = sensitivity
                except Exception:
                    sensitivities[param] = 0.0
        
        return sensitivities
    
    def risk_assessment(self, assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk factors for the project."""
        risk_factors = {}
        
        # Price risk
        ppa_price = assumptions.get('ppa_price_eur_kwh', 0.05)
        if ppa_price < 0.04:
            risk_factors['price_risk'] = 'HIGH - Very low PPA price'
        elif ppa_price < 0.05:
            risk_factors['price_risk'] = 'MEDIUM - Below average PPA price'
        else:
            risk_factors['price_risk'] = 'LOW - Good PPA price'
        
        # Technology risk
        capacity_factor = assumptions.get('capacity_factor', 0.25)
        if capacity_factor < 0.2:
            risk_factors['technology_risk'] = 'HIGH - Low capacity factor'
        elif capacity_factor < 0.25:
            risk_factors['technology_risk'] = 'MEDIUM - Average capacity factor'
        else:
            risk_factors['technology_risk'] = 'LOW - High capacity factor'
        
        # Financial risk
        debt_ratio = assumptions.get('debt_ratio', 0.75)
        if debt_ratio > 0.8:
            risk_factors['financial_risk'] = 'HIGH - Very high leverage'
        elif debt_ratio > 0.75:
            risk_factors['financial_risk'] = 'MEDIUM - High leverage'
        else:
            risk_factors['financial_risk'] = 'LOW - Conservative leverage'
        
        # Market risk
        capex = assumptions.get('capex_eur_kw', 1200)
        if capex > 1400:
            risk_factors['market_risk'] = 'HIGH - High CAPEX vs market'
        elif capex > 1200:
            risk_factors['market_risk'] = 'MEDIUM - Above average CAPEX'
        else:
            risk_factors['market_risk'] = 'LOW - Competitive CAPEX'
        
        # Overall risk score
        risk_scores = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}
        avg_risk = sum(risk_scores.get(risk.split(' - ')[0], 2) for risk in risk_factors.values()) / len(risk_factors)
        
        if avg_risk <= 1.5:
            overall_risk = 'LOW'
        elif avg_risk <= 2.5:
            overall_risk = 'MEDIUM'
        else:
            overall_risk = 'HIGH'
        
        return {
            'risk_factors': risk_factors,
            'overall_risk': overall_risk,
            'risk_score': avg_risk,
            'recommendations': self._get_risk_recommendations(risk_factors)
        }
    
    def benchmark_comparison(self, assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """Compare project metrics against industry benchmarks."""
        benchmarks = {
            'capacity_factor': {'excellent': 0.3, 'good': 0.25, 'average': 0.2},
            'capex_eur_kw': {'excellent': 1000, 'good': 1200, 'average': 1400},
            'ppa_price_eur_kwh': {'excellent': 0.06, 'good': 0.05, 'average': 0.04},
            'debt_ratio': {'excellent': 0.7, 'good': 0.75, 'average': 0.8}
        }
        
        comparisons = {}
        for param, thresholds in benchmarks.items():
            value = assumptions.get(param, 0)
            
            if param == 'capex_eur_kw':  # Lower is better for CAPEX
                if value <= thresholds['excellent']:
                    rating = 'EXCELLENT'
                elif value <= thresholds['good']:
                    rating = 'GOOD'
                elif value <= thresholds['average']:
                    rating = 'AVERAGE'
                else:
                    rating = 'BELOW_AVERAGE'
            else:  # Higher is better for other metrics
                if value >= thresholds['excellent']:
                    rating = 'EXCELLENT'
                elif value >= thresholds['good']:
                    rating = 'GOOD'
                elif value >= thresholds['average']:
                    rating = 'AVERAGE'
                else:
                    rating = 'BELOW_AVERAGE'
            
            comparisons[param] = {
                'value': value,
                'rating': rating,
                'benchmarks': thresholds
            }
        
        return comparisons
    
    def get_model_statistics(self) -> Dict[str, Any]:
        """Get ML model performance statistics."""
        if not SKLEARN_AVAILABLE:
            return {'status': 'ML models not available'}
        
        stats = {
            'available_models': [target.value for target in self.models.keys()],
            'feature_columns': self.feature_columns,
            'model_types': {target.value: type(model).__name__ for target, model in self.models.items()},
            'sklearn_available': SKLEARN_AVAILABLE
        }
        
        return stats
    
    def retrain_models(self, new_data: pd.DataFrame = None):
        """Retrain models with new data."""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("Cannot retrain models - scikit-learn not available")
            return
        
        if new_data is None:
            # Generate new synthetic data
            generator = FinancialDataGenerator()
            new_data = generator.generate_training_data(2000)  # Larger dataset
        
        targets = [PredictionTarget.IRR_EQUITY, PredictionTarget.NPV_EQUITY, PredictionTarget.LCOE]
        
        for target in targets:
            try:
                self._train_model(new_data, target)
                self.logger.info(f"Retrained model for {target.value}")
            except Exception as e:
                self.logger.error(f"Failed to retrain model for {target.value}: {e}")
    
    def _get_fallback_prediction(self, target: PredictionTarget) -> PredictionResult:
        """Get fallback prediction when models are not available."""
        fallback_values = {
            PredictionTarget.IRR_EQUITY: 0.12,
            PredictionTarget.NPV_EQUITY: 1000000,
            PredictionTarget.LCOE: 0.06
        }
        
        return PredictionResult(
            target=target,
            predicted_value=fallback_values.get(target, 0.1),
            confidence_interval=(0.08, 0.15),
            feature_contributions={},
            model_confidence=0.5,
            recommendations=["ML models not available - using industry averages"]
        )
    
    def _get_parameter_range(self, param: str, original_value: float) -> List[float]:
        """Get parameter range for optimization."""
        if original_value <= 0:
            original_value = {
                'capacity_mw': 10.0,
                'capex_eur_kw': 1200.0,
                'capacity_factor': 0.25,
                'ppa_price_eur_kwh': 0.05,
                'debt_ratio': 0.75,
                'interest_rate': 0.06,
                'discount_rate': 0.08
            }.get(param, 1.0)
        
        ranges = {
            'capacity_mw': np.linspace(original_value * 0.5, original_value * 2, 10),
            'ppa_price_eur_kwh': np.linspace(original_value * 0.8, original_value * 1.3, 10),
            'capex_eur_kw': np.linspace(original_value * 0.7, original_value * 1.3, 10),
            'capacity_factor': np.linspace(max(0.15, original_value * 0.8), min(0.4, original_value * 1.2), 10),
            'debt_ratio': np.linspace(0.5, 0.9, 10),
            'interest_rate': np.linspace(0.03, 0.1, 10),
            'discount_rate': np.linspace(0.06, 0.15, 10)
        }
        
        return ranges.get(param, [original_value])
    
    def _get_risk_recommendations(self, risk_factors: Dict[str, str]) -> List[str]:
        """Get recommendations based on risk assessment."""
        recommendations = []
        
        for factor, risk_level in risk_factors.items():
            if 'HIGH' in risk_level:
                if 'price' in factor:
                    recommendations.append("Consider long-term PPA with escalation clauses")
                elif 'technology' in factor:
                    recommendations.append("Evaluate higher efficiency PV modules")
                elif 'financial' in factor:
                    recommendations.append("Consider reducing debt ratio or finding cheaper financing")
                elif 'market' in factor:
                    recommendations.append("Review CAPEX assumptions and supplier quotes")
        
        if not recommendations:
            recommendations.append("Risk profile is acceptable for the project")
        
        return recommendations


# Global ML service instance
ml_prediction_service = MLPredictionService() if SKLEARN_AVAILABLE else None


# Convenience functions
def get_ml_service() -> Optional[MLPredictionService]:
    """Get global ML prediction service instance."""
    return ml_prediction_service


def predict_financial_metrics(assumptions: Dict[str, Any]) -> Dict[str, Any]:
    """Predict financial metrics using ML if available."""
    if ml_prediction_service:
        try:
            predictions = ml_prediction_service.predict_multiple(assumptions)
            return {
                target.value: {
                    'predicted_value': result.predicted_value,
                    'confidence_interval': result.confidence_interval,
                    'recommendations': result.recommendations
                }
                for target, result in predictions.items()
            }
        except Exception as e:
            logging.getLogger(__name__).error(f"ML prediction failed: {e}")
    
    # Fallback predictions
    return {
        'irr_equity': {
            'predicted_value': 0.12,
            'confidence_interval': (0.10, 0.14),
            'recommendations': ['ML predictions not available - using fallback values']
        },
        'npv_equity': {
            'predicted_value': 1000000,
            'confidence_interval': (800000, 1200000),
            'recommendations': ['Verify assumptions with detailed DCF model']
        },
        'lcoe': {
            'predicted_value': 0.06,
            'confidence_interval': (0.05, 0.07),
            'recommendations': ['Compare with regional LCOE benchmarks']
        }
    }
