import asyncio
import flet as ft
from components.ui.enhanced_progress_overlay import EnhancedProgressOverlay

async def long_running_task(page: ft.Page):
    overlay = EnhancedProgressOverlay(page)
    await overlay.show("Task starting...")
    
    for progress in range(0, 101, 10):
        overlay.update_progress(progress, f"Progress at {progress}%")
        await asyncio.sleep(0.5)  # Simulate work for 0.5 seconds
    
    await overlay.hide("Task complete!")


def main(page: ft.Page):
    page.title = "Long Running Task Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 600
    page.window_height = 400
    
    test_button = ft.ElevatedButton(
        "Run Long Task",
        on_click=lambda _: page.run_task(long_running_task, page)
    )
    
    page.add(test_button)


if __name__ == "__main__":
    ft.app(target=main)

