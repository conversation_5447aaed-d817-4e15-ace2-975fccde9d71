# Build and distribution artifacts
/output
dist/
build/
*.spec
*.AppImage

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
*.pyd

# Virtual environments
venv/
env/
.venv/
.env/

# Database files (keep structure but not data)
*.db
*.sqlite
*.sqlite3

# Backup files
data/backups/
*.bak
*.backup
*.gz

# Log files
*.log
logs/

# Temporary files and test outputs
temp_*
*.tmp
interactive_dashboard_*.html
test_*.png
*_test_*.png

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation and analysis files (generated)
*_SUMMARY.md
*_FIX*.md
*_ANALYSIS.md
*_REPORT.md

# ML models (if large)
ml_models/*.pkl
ml_models/*.joblib
ml_models/*.model

# Test and debug files
debug_*.py
test_*.py
simple_*.py
example_*.py
run_*.py
validate_*.py