"""
Enhanced UI Animations
======================

Smooth animations and transitions for improved user experience.
"""

import flet as ft
from typing import Optional, Callable, Any
import asyncio
import math


class AnimationController:
    """Controller for managing UI animations."""
    
    def __init__(self):
        self.animations = {}
        self.running = {}
    
    async def animate_value(
        self, 
        start: float, 
        end: float, 
        duration: float = 0.3,
        easing: str = "ease_in_out",
        on_update: Optional[Callable[[float], None]] = None,
        on_complete: Optional[Callable[[], None]] = None
    ) -> None:
        """Animate a value from start to end with easing."""
        steps = int(duration * 60)  # 60 FPS
        
        for i in range(steps + 1):
            progress = i / steps
            
            # Apply easing
            if easing == "ease_in_out":
                progress = self._ease_in_out_cubic(progress)
            elif easing == "ease_in":
                progress = self._ease_in_cubic(progress)
            elif easing == "ease_out":
                progress = self._ease_out_cubic(progress)
            
            value = start + (end - start) * progress
            
            if on_update:
                on_update(value)
            
            await asyncio.sleep(1 / 60)  # 60 FPS
        
        if on_complete:
            on_complete()
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic ease-in-out function."""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def _ease_in_cubic(self, t: float) -> float:
        """Cubic ease-in function."""
        return t * t * t
    
    def _ease_out_cubic(self, t: float) -> float:
        """Cubic ease-out function."""
        return 1 - pow(1 - t, 3)


class AnimatedProgressBar:
    """Progress bar with smooth animations."""
    
    def __init__(
        self,
        width: int = 400,
        height: int = 8,
        color: str = ft.Colors.PRIMARY,
        bgcolor: str = ft.Colors.GREY_300,
        animate_duration: int = 300
    ):
        self.width = width
        self.height = height
        self.color = color
        self.bgcolor = bgcolor
        self.animate_duration = animate_duration
        self._current_value = 0
        self._target_value = 0
        self.animation_controller = AnimationController()
        
        # Create components
        self.progress_bar = ft.ProgressBar(
            width=width,
            height=height,
            value=0,
            color=color,
            bgcolor=bgcolor
        )
        
        # Add glow effect container
        self.glow_container = ft.Container(
            content=self.progress_bar,
            border_radius=height / 2,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=0,
                color=ft.Colors.with_opacity(0, color),
                offset=ft.Offset(0, 0)
            )
        )
    
    async def set_value(self, value: float):
        """Set progress value with immediate update (simplified for stability)."""
        self._target_value = max(0, min(1, value))
        self._current_value = self._target_value
        
        # Direct update without complex animation
        self.progress_bar.value = self._target_value
        
        # Simple glow effect based on progress
        if self._target_value > 0 and self._target_value < 1:
            glow_intensity = 0.3
            self.glow_container.shadow = ft.BoxShadow(
                spread_radius=1,
                blur_radius=4,
                color=ft.Colors.with_opacity(glow_intensity, self.color),
                offset=ft.Offset(0, 0)
            )
        else:
            self.glow_container.shadow = ft.BoxShadow(
                spread_radius=0,
                blur_radius=0,
                color=ft.Colors.with_opacity(0, self.color),
                offset=ft.Offset(0, 0)
            )
        
        # Force immediate update
        try:
            self.glow_container.update()
        except:
            pass
    
    def _update_progress(self, value: float):
        """Update progress bar value and glow effect."""
        self.progress_bar.value = value
        
        # Update glow effect based on progress
        if value > 0 and value < 1:
            glow_intensity = 0.3 + (0.2 * math.sin(value * math.pi))
            self.glow_container.shadow = ft.BoxShadow(
                spread_radius=2,
                blur_radius=8,
                color=ft.Colors.with_opacity(glow_intensity, self.color),
                offset=ft.Offset(0, 0)
            )
        else:
            self.glow_container.shadow = ft.BoxShadow(
                spread_radius=0,
                blur_radius=0,
                color=ft.Colors.with_opacity(0, self.color),
                offset=ft.Offset(0, 0)
            )
        
        try:
            self.glow_container.update()
        except:
            pass


class PulsingIcon:
    """Icon with pulsing animation."""
    
    def __init__(
        self,
        icon: str,
        size: int = 40,
        color: str = ft.Colors.PRIMARY,
        pulse_duration: float = 1.5
    ):
        self.icon = icon
        self.size = size
        self.color = color
        self.pulse_duration = pulse_duration
        self._is_pulsing = False
        
        # Create components
        self.icon_control = ft.Icon(
            icon,
            size=size,
            color=color
        )
        
        self.container = ft.Container(
            content=self.icon_control,
            animate_scale=300
        )
    
    async def start_pulsing(self):
        """Start the pulsing animation (simplified)."""
        self._is_pulsing = True
        
        while self._is_pulsing:
            try:
                self.container.scale = 1.05
                self.container.update()
                await asyncio.sleep(0.8)
                
                if self._is_pulsing:
                    self.container.scale = 1.0
                    self.container.update()
                    await asyncio.sleep(0.8)
            except Exception:
                # Ignore animation errors and continue
                await asyncio.sleep(1)
    
    def stop_pulsing(self):
        """Stop the pulsing animation."""
        self._is_pulsing = False
        self.container.scale = 1.0
        self.container.update()


class FadeTransition:
    """Helper class for fade transitions."""
    
    @staticmethod
    async def fade_in(control: ft.Control, duration: float = 0.3):
        """Fade in a control."""
        control.opacity = 0
        control.visible = True
        control.update()
        
        animation_controller = AnimationController()
        await animation_controller.animate_value(
            start=0,
            end=1,
            duration=duration,
            on_update=lambda v: setattr(control, 'opacity', v)
        )
        control.update()
    
    @staticmethod
    async def fade_out(control: ft.Control, duration: float = 0.3):
        """Fade out a control."""
        animation_controller = AnimationController()
        await animation_controller.animate_value(
            start=1,
            end=0,
            duration=duration,
            on_update=lambda v: setattr(control, 'opacity', v)
        )
        control.visible = False
        control.update()


class SlideTransition:
    """Helper class for slide transitions."""
    
    @staticmethod
    async def slide_in_from_bottom(control: ft.Control, duration: float = 0.3):
        """Slide in a control from bottom."""
        # Initial position
        control.offset = ft.transform.Offset(0, 1)
        control.visible = True
        control.update()
        
        # Animate to final position
        control.animate = int(duration * 1000)  # Duration in milliseconds
        control.offset = ft.Offset(0, 0)
        control.update()
        
        await asyncio.sleep(duration)
    
    @staticmethod
    async def slide_out_to_bottom(control: ft.Control, duration: float = 0.3):
        """Slide out a control to bottom."""
        control.animate = int(duration * 1000)  # Duration in milliseconds
        control.offset = ft.transform.Offset(0, 1)
        control.update()
        
        await asyncio.sleep(duration)
        control.visible = False
        control.update()


class LoadingDots:
    """Animated loading dots."""
    
    def __init__(self, size: int = 8, color: str = ft.Colors.PRIMARY):
        self.size = size
        self.color = color
        self._animating = False
        
        # Create dots
        self.dots = []
        for i in range(3):
            dot = ft.Container(
                width=size,
                height=size,
                bgcolor=color,
                border_radius=size / 2,
                opacity=0.3
            )
            self.dots.append(dot)
        
        # Create container
        self.container = ft.Row(
            controls=self.dots,
            spacing=size / 2
        )
    
    async def start_animation(self):
        """Start the loading animation (simplified)."""
        self._animating = True
        
        while self._animating:
            try:
                for i, dot in enumerate(self.dots):
                    if not self._animating:
                        break
                    dot.opacity = 1
                    self.container.update()
                    await asyncio.sleep(0.3)
                    
                    if self._animating:
                        dot.opacity = 0.3
                        self.container.update()
                        await asyncio.sleep(0.1)
            except Exception:
                # Ignore animation errors and continue
                await asyncio.sleep(0.5)
    
    def stop_animation(self):
        """Stop the loading animation."""
        self._animating = False
        for dot in self.dots:
            dot.opacity = 0.3
        self.container.update()
