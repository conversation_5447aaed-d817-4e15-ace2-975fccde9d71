"""
Project Parameters Form
=======================

Form component for project parameters data entry.
"""

import flet as ft
from typing import Optional, Callable, Any, Dict, Union

from models.project_assumptions import EnhancedProjectAssumptions


class ProjectParamsForm:
    """Form component for project parameters."""
    
    def __init__(self, project_assumptions: EnhancedProjectAssumptions):
        self.project_assumptions = project_assumptions
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        
        # Form fields - will be created in build()
        self.fields: Dict[str, Union[ft.TextField, ft.Dropdown]] = {}
    
    def build(self) -> ft.Container:
        """Build the project parameters form."""
        
        # Technical parameters section
        technical_section = self._create_technical_section()
        
        # Financial parameters section
        financial_section = self._create_financial_section()
        
        # Grant parameters section
        grant_section = self._create_grant_section()
        
        return ft.Container(
            content=ft.Column([
                technical_section,
                ft.Container(height=20),
                financial_section,
                ft.Container(height=20),
                grant_section
            ]),
            padding=15
        )
    
    def _create_technical_section(self) -> ft.Card:
        """Create technical parameters section."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.SETTINGS, color=ft.colors.BLUE_600),
                        ft.Text("⚙️ Technical Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.colors.BLUE_600)
                    ], alignment=ft.MainAxisAlignment.START),
                    ft.Divider(height=10),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("capacity_mw", "Capacity (MW)", "10.0", tooltip="Total installed capacity of the project")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("project_life_years", "Project Life (years)", "25", tooltip="Expected operational lifetime of the project")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("production_mwh_year1", "Production Year 1 (MWh)", "18000", tooltip="Expected energy production in first year")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("degradation_rate", "Degradation Rate (%)", "0.5", tooltip="Annual degradation rate as percentage")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("capex_meur", "CAPEX (M EUR)", "8.5", tooltip="Capital expenditure in millions of euros")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("opex_keuros_year1", "OPEX Year 1 (k EUR)", "180", tooltip="Operating expenditure in first year (thousands of euros)")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_location_field("project_location", "Project Location", "Morocco", tooltip="Geographic location of the project")
                        ], col={"sm": 12})
                    ])
                ]),
                padding=20
            ),
            elevation=2,
            margin=ft.margin.only(bottom=10)
        )
    
    def _create_financial_section(self) -> ft.Card:
        """Create financial parameters section."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.EURO_SYMBOL, color=ft.colors.GREEN_600),
                        ft.Text("💶 Financial Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.colors.GREEN_600)
                    ], alignment=ft.MainAxisAlignment.START),
                    ft.Divider(height=10),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("ppa_price_eur_kwh", "PPA Price (EUR/kWh)", "0.045", tooltip="Power Purchase Agreement price in euros per kWh")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("ppa_escalation", "PPA Escalation (%)", "0.0", tooltip="Annual escalation rate of PPA price as percentage")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("debt_ratio", "Debt Ratio", "0.75", tooltip="Debt-to-total-financing ratio (0-1)")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("interest_rate", "Interest Rate (%)", "6.0", tooltip="Annual interest rate on debt as percentage")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("debt_years", "Debt Tenor (years)", "15", tooltip="Duration of debt repayment period")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("discount_rate", "Discount Rate (%)", "8.0", tooltip="Discount rate for NPV calculations as percentage")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("tax_rate", "Tax Rate (%)", "15.0", tooltip="Corporate tax rate as percentage")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("land_lease_eur_mw_year", "Land Lease (EUR/MW/year)", "2000", tooltip="Annual land lease cost per MW in euros")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("tax_holiday", "Taxation Grace Period (years)", "5", tooltip="Number of years with tax exemption")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("grace_years", "Debt Grace Period (years)", "2", tooltip="Number of years before debt principal repayment starts")
                        ], col={"sm": 6})
                    ])
                ]),
                padding=20
            ),
            elevation=2,
            margin=ft.margin.only(bottom=10)
        )
    
    def _create_grant_section(self) -> ft.Card:
        """Create grant parameters section."""
        return ft.Card(
            content=ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.Icons.CARD_GIFTCARD, color=ft.colors.PURPLE_600),
                        ft.Text("🎁 Grant Parameters", size=18, weight=ft.FontWeight.BOLD, color=ft.colors.PURPLE_600)
                    ], alignment=ft.MainAxisAlignment.START),
                    ft.Divider(height=10),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("grant_meur_italy", "Italian Grant (M EUR)", "0.0", tooltip="Grant funding from Italian sources in millions of euros")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("grant_meur_masen", "MASEN Grant (M EUR)", "0.0", tooltip="Grant funding from MASEN in millions of euros")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("grant_meur_connection", "Connection Grant (M EUR)", "0.0", tooltip="Grant funding for grid connection in millions of euros")
                        ], col={"sm": 6}),
                        ft.Column([
                            self._create_field("grant_meur_simest_africa", "SIMEST African Fund (M EUR)", "0.0", tooltip="Grant funding from SIMEST African Fund in millions of euros")
                        ], col={"sm": 6})
                    ]),
                    
                    ft.ResponsiveRow([
                        ft.Column([
                            self._create_field("grant_meur_cri", "CRI Regional Investment Center Grant (M EUR)", "0.0", tooltip="Grant funding from CRI Regional Investment Center in millions of euros")
                        ], col={"sm": 12})
                    ])
                ]),
                padding=20
            ),
            elevation=2,
            margin=ft.margin.only(bottom=10)
        )
    
    def _create_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.TextField:
        """Create a form field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)
        
        # Handle percentage display for percentage fields
        if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
            display_value = float(current_value) * 100.0 if current_value else 0.0
        else:
            display_value = current_value
        
        field = ft.TextField(
            label=label,
            value=str(display_value),
            hint_text=f"Enter {label.lower()}",
            keyboard_type=ft.KeyboardType.NUMBER,
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )
        
        self.fields[field_name] = field
        return field
    
    def _create_location_field(self, field_name: str, label: str, default_value: str, tooltip: str = None) -> ft.Dropdown:
        """Create a location dropdown field."""
        current_value = getattr(self.project_assumptions, field_name, default_value)
        
        dropdown = ft.Dropdown(
            label=label,
            value=current_value,
            options=[
                ft.dropdown.Option("Morocco"),
                ft.dropdown.Option("Italy"),
                ft.dropdown.Option("Spain"),
                ft.dropdown.Option("France"),
                ft.dropdown.Option("Tunisia"),
                ft.dropdown.Option("Algeria"),
                ft.dropdown.Option("Other")
            ],
            on_change=lambda e, name=field_name: self._on_field_changed(name, e.control.value),
            expand=True,
            tooltip=tooltip
        )
        
        self.fields[field_name] = dropdown
        return dropdown
    
    def _on_field_changed(self, field_name: str, value: str):
        """Handle field value changes."""
        try:
            # Convert to appropriate type
            if field_name in ['capacity_mw', 'production_mwh_year1', 'capex_meur', 'opex_keuros_year1',
                             'ppa_price_eur_kwh', 'debt_ratio', 'interest_rate', 'discount_rate',
                             'tax_rate', 'degradation_rate', 'ppa_escalation', 'land_lease_eur_mw_year',
                             'grant_meur_italy', 'grant_meur_masen', 'grant_meur_connection', 'grant_meur_simest_africa',
                             'grant_meur_cri']:
                numeric_value = float(value) if value else 0.0
                
                # Convert percentages to decimals
                if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                    numeric_value = numeric_value / 100.0
                
                if self.on_data_changed:
                    self.on_data_changed(field_name, numeric_value)
            
            elif field_name in ['project_life_years', 'debt_years', 'tax_holiday', 'grace_years']:
                int_value = int(float(value)) if value else 0
                if self.on_data_changed:
                    self.on_data_changed(field_name, int_value)
            
            else:
                if self.on_data_changed:
                    self.on_data_changed(field_name, value)
        
        except ValueError:
            # Invalid number, ignore
            pass
    
    def update_data(self, project_assumptions: EnhancedProjectAssumptions):
        """Update form with new project assumptions data."""
        self.project_assumptions = project_assumptions

        # Update all fields
        for field_name, field in self.fields.items():
            value = getattr(project_assumptions, field_name, 0)

            # Convert decimals to percentages for display
            if field_name in ['degradation_rate', 'ppa_escalation', 'interest_rate', 'discount_rate', 'tax_rate']:
                value = value * 100.0

            field.value = str(value)

        # Trigger UI update for all fields
        for field in self.fields.values():
            field.update()
    
    def validate(self) -> Dict[str, str]:
        """Validate form data."""
        return self.project_assumptions.validate_all()
    
    def get_data(self) -> EnhancedProjectAssumptions:
        """Get current form data as EnhancedProjectAssumptions."""
        return self.project_assumptions
