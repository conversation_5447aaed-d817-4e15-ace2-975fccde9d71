# Enhanced Financial Model - API Reference

## Overview

This document provides a comprehensive reference for the Enhanced Financial Model application's internal APIs and interfaces.

## Core Models

### ClientProfile

**Location**: `src/new_app/models/client_profile.py`

```python
@dataclass
class ClientProfile:
    company_name: str = ""
    client_name: str = ""
    contact_email: str = ""
    phone: str = ""
    project_name: str = ""
    project_location: str = ""
    project_capacity_mw: Optional[float] = None
    preferred_currency: str = "EUR"
    report_date: str = field(default_factory=lambda: datetime.now().isoformat())
```

**Methods:**

- `validate() -> Dict[str, str]`: Validate profile data
- `is_complete() -> bool`: Check if profile is complete
- `get_display_name() -> str`: Get formatted display name
- `get_clean_company_name() -> str`: Get filesystem-safe company name
- `to_dict() -> Dict[str, Any]`: Serialize to dictionary
- `from_dict(data: Dict[str, Any]) -> ClientProfile`: Deserialize from dictionary

### EnhancedProjectAssumptions

**Location**: `src/new_app/models/project_assumptions.py`

```python
@dataclass
class EnhancedProjectAssumptions:
    # Technical parameters
    capacity_mw: float = 10.0
    production_mwh_year1: float = 18000.0
    degradation_rate: float = 0.005
    project_life_years: int = 25
    
    # Financial parameters
    capex_meur: float = 8.5
    opex_keuros_year1: float = 180.0
    ppa_price_eur_kwh: float = 0.045
    ppa_escalation: float = 0.0
    debt_ratio: float = 0.75
    interest_rate: float = 0.06
    debt_years: int = 15
    discount_rate: float = 0.08
    tax_rate: float = 0.30
    land_lease_eur_mw_year: float = 2000.0
    
    # Grant parameters
    grant_meur_italy: float = 0.0
    grant_meur_masen: float = 0.0
    grant_meur_connection: float = 0.0
    grant_meur_simest_africa: float = 0.0
```

**Methods:**

- `validate_all() -> Dict[str, str]`: Comprehensive validation
- `calculate_capacity_factor() -> float`: Calculate capacity factor
- `calculate_total_grants() -> float`: Calculate total grants
- `calculate_grant_percentage() -> float`: Calculate grant percentage of CAPEX
- `get_financial_summary() -> Dict[str, Any]`: Get financial summary
- `copy_with_modifications(**kwargs) -> EnhancedProjectAssumptions`: Create modified copy

## Services

### FinancialModelService

**Location**: `src/new_app/services/financial_service.py`

```python
class FinancialModelService:
    def run_financial_model(self, 
                           assumptions: EnhancedProjectAssumptions,
                           progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Run comprehensive financial model."""
        
    def run_sensitivity_analysis(self,
                                assumptions: EnhancedProjectAssumptions,
                                variables: List[str]) -> pd.DataFrame:
        """Run sensitivity analysis on specified variables."""
        
    def run_monte_carlo_simulation(self,
                                  assumptions: EnhancedProjectAssumptions,
                                  n_simulations: int = 1000,
                                  progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Run Monte Carlo simulation."""
        
    def run_scenario_analysis(self,
                             assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Run scenario analysis with predefined scenarios."""
```

**Return Formats:**

**Financial Model Results:**
```python
{
    'kpis': {
        'IRR_project': float,      # Project IRR (decimal)
        'IRR_equity': float,       # Equity IRR (decimal)
        'NPV_project': float,      # Project NPV (EUR)
        'NPV_equity': float,       # Equity NPV (EUR)
        'LCOE_eur_kwh': float,     # LCOE (EUR/kWh)
        'Min_DSCR': float,         # Minimum DSCR
        'Avg_DSCR': float,         # Average DSCR
        'Payback_years': float     # Payback period (years)
    },
    'cashflow': pd.DataFrame,      # Detailed cashflow
    'assumptions': dict            # Input assumptions
}
```

### ValidationService

**Location**: `src/new_app/services/validation_service.py`

```python
class ValidationService:
    def validate_model(self,
                      assumptions: EnhancedProjectAssumptions,
                      kpis: Dict[str, float],
                      cashflow: pd.DataFrame) -> ValidationResult:
        """Comprehensive model validation."""
        
    def generate_benchmark_comparison(self,
                                    assumptions: EnhancedProjectAssumptions,
                                    kpis: Dict[str, float]) -> Dict[str, Any]:
        """Generate benchmark comparison analysis."""
```

**ValidationResult:**
```python
@dataclass
class ValidationResult:
    is_valid: bool
    warnings: List[str]
    errors: List[str]
    recommendations: List[str]
```

### ExportService

**Location**: `src/new_app/services/export_service.py`

```python
class ExportService:
    def export_excel_report(self,
                           client_profile: ClientProfile,
                           assumptions: EnhancedProjectAssumptions,
                           financial_results: Dict[str, Any]) -> Path:
        """Export comprehensive Excel report."""
        
    def export_docx_report(self,
                          client_profile: ClientProfile,
                          assumptions: EnhancedProjectAssumptions,
                          financial_results: Dict[str, Any]) -> Path:
        """Export DOCX report."""
        
    def export_html_report(self,
                          client_profile: ClientProfile,
                          assumptions: EnhancedProjectAssumptions,
                          financial_results: Dict[str, Any]) -> Path:
        """Export HTML report."""
        
    def export_json_data(self,
                        client_profile: ClientProfile,
                        assumptions: EnhancedProjectAssumptions,
                        financial_results: Dict[str, Any]) -> Path:
        """Export raw JSON data."""
```

## Utility Functions

### ValidationUtils

**Location**: `src/new_app/utils/validation_utils.py`

```python
class ValidationUtils:
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format."""
        
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Validate phone number format."""
        
    @staticmethod
    def validate_positive_number(value: Union[int, float], allow_zero: bool = False) -> bool:
        """Validate positive number."""
        
    @staticmethod
    def validate_percentage(value: Union[int, float], min_val: float = 0.0, max_val: float = 1.0) -> bool:
        """Validate percentage value."""
        
    @staticmethod
    def validate_capacity_factor(production_mwh: float, capacity_mw: float) -> Dict[str, Any]:
        """Validate capacity factor calculation."""
        
    @staticmethod
    def validate_financial_ratios(debt_ratio: float, interest_rate: float, discount_rate: float) -> Dict[str, Any]:
        """Validate financial ratios."""
```

### FormattingUtils

**Location**: `src/new_app/utils/formatting_utils.py`

```python
class FormattingUtils:
    @staticmethod
    def format_currency(value: Union[int, float], currency: str = "EUR", precision: int = 2) -> str:
        """Format currency with scaling."""
        
    @staticmethod
    def format_percentage(value: Union[int, float], precision: int = 1, multiply_by_100: bool = True) -> str:
        """Format percentage value."""
        
    @staticmethod
    def format_number(value: Union[int, float], precision: int = 2, use_thousands_separator: bool = True) -> str:
        """Format number with thousands separator."""
        
    @staticmethod
    def format_energy(value: Union[int, float], unit: str = "MWh", precision: int = 1) -> str:
        """Format energy values with appropriate units."""
        
    @staticmethod
    def format_power(value: Union[int, float], unit: str = "MW", precision: int = 1) -> str:
        """Format power values with appropriate units."""
```

### FileUtils

**Location**: `src/new_app/utils/file_utils.py`

```python
class FileUtils:
    def create_timestamped_output_directory(self, client_profile: ClientProfile) -> Dict[str, Path]:
        """Create timestamped output directory structure."""
        
    def generate_filename(self, file_type: str, client_profile: ClientProfile, include_timestamp: bool = True) -> str:
        """Generate filename based on configuration."""
        
    def save_json_data(self, data: Dict, filepath: Path) -> bool:
        """Save data as JSON file."""
        
    def load_json_data(self, filepath: Path) -> Optional[Dict]:
        """Load data from JSON file."""
```

## Chart Components

### ChartFactory

**Location**: `src/new_app/components/charts/chart_factory.py`

```python
class ChartFactory:
    def create_kpi_gauge(self, title: str, current_value: float, target_value: float, max_value: float) -> ft.Container:
        """Create KPI gauge chart."""
        
    def create_bar_chart(self, data: Dict[str, float], title: str, x_label: str = "", y_label: str = "") -> ft.Container:
        """Create bar chart."""
        
    def create_line_chart(self, data: pd.DataFrame, title: str, x_column: str, y_columns: List[str]) -> ft.Container:
        """Create line chart."""
        
    def create_pie_chart(self, data: Dict[str, float], title: str) -> ft.Container:
        """Create pie chart."""
        
    def create_waterfall_chart(self, categories: List[str], values: List[float], title: str) -> ft.Container:
        """Create waterfall chart."""
```

## Configuration

### AppConfig

**Location**: `src/new_app/config/app_config.py`

```python
@dataclass
class AppConfig:
    app_name: str = "Enhanced Financial Model"
    app_version: str = "2.0.0"
    company_name: str = "Agevolami SRL"
    default_currency: str = "EUR"
    export_formats: list = field(default_factory=lambda: ["Excel", "DOCX", "HTML", "JSON"])
    max_monte_carlo_simulations: int = 10000
    default_monte_carlo_simulations: int = 1000
```

### UIConfig

**Location**: `src/new_app/config/ui_config.py`

```python
@dataclass
class UIConfig:
    theme_mode: str = "light"
    primary_color: str = ft.Colors.BLUE
    page_padding: int = 20
    card_padding: int = 15
    chart_height: int = 300
    chart_width: int = 400
```

## Event System

### Callbacks

Views communicate with the controller through callbacks:

```python
# View callback signatures
on_navigate: Callable[[TabState], None]
on_data_changed: Callable[[str, Any], None]
on_action_requested: Callable[[str, Dict[str, Any]], None]
on_status_update: Callable[[str, str], None]
```

### Action Types

Standard action types for `on_action_requested`:

- `"run_financial_model"`: Run basic financial model
- `"run_comprehensive_analysis"`: Run complete analysis
- `"run_location_comparison"`: Run location comparison
- `"run_sensitivity_analysis"`: Run sensitivity analysis
- `"run_monte_carlo"`: Run Monte Carlo simulation
- `"run_scenario_analysis"`: Run scenario analysis
- `"quick_export"`: Quick export
- `"comprehensive_export"`: Comprehensive export
- `"save_configuration"`: Save current configuration
- `"load_preset"`: Load preset configuration

## Error Handling

### Exception Types

Custom exceptions for specific error conditions:

```python
class ValidationError(Exception):
    """Raised when validation fails."""
    pass

class CalculationError(Exception):
    """Raised when financial calculations fail."""
    pass

class ExportError(Exception):
    """Raised when export operations fail."""
    pass
```

### Error Response Format

```python
{
    'success': bool,
    'error_type': str,
    'error_message': str,
    'details': Optional[Dict[str, Any]]
}
```

## Constants

### Location Constants

**Location**: `src/new_app/models/location_config.py`

```python
MOROCCO_LOCATIONS = {
    "ouarzazate": {
        "name": "Ouarzazate",
        "irradiation": 2100,
        "capacity_factor": 0.24
    },
    # ... other locations
}
```

### Financial Constants

```python
# Default financial parameters
DEFAULT_DEBT_RATIO = 0.75
DEFAULT_INTEREST_RATE = 0.06
DEFAULT_DISCOUNT_RATE = 0.08
DEFAULT_TAX_RATE = 0.30
DEFAULT_PROJECT_LIFE = 25
DEFAULT_DEBT_TENOR = 15

# Validation ranges
MIN_CAPACITY_MW = 0.1
MAX_CAPACITY_MW = 1000.0
MIN_IRR = -0.5
MAX_IRR = 1.0
MIN_DSCR = 0.5
MAX_DSCR = 10.0
```

---

**Author**: Abdelhalim Serhani, Agevolami SRL  
**Version**: 2.0.0  
**Last Updated**: 2024
