# Enhanced Financial Model - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Enhanced Financial Model application in different environments.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 2 GB free space
- **Display**: 1280x720 minimum resolution

### Recommended Requirements
- **Python**: 3.10 or higher
- **RAM**: 16 GB
- **Storage**: 5 GB free space
- **Display**: 1920x1080 or higher

## Installation Methods

### Method 1: Standard Python Installation

1. **Install Python**
   ```bash
   # Verify Python installation
   python --version
   # Should be 3.8 or higher
   ```

2. **Clone/Download Application**
   ```bash
   # If using git
   git clone <repository-url>
   cd enhanced-financial-model
   
   # Or download and extract ZIP file
   ```

3. **Create Virtual Environment**
   ```bash
   python -m venv venv
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   
   # macOS/Linux:
   source venv/bin/activate
   ```

4. **Install Dependencies**
   ```bash
   pip install -r src/new_app/requirements.txt
   ```

5. **Run Application**
   ```bash
   python src/new_app/main.py
   ```

### Method 2: Conda Installation

1. **Install Anaconda/Miniconda**
   - Download from https://www.anaconda.com/

2. **Create Conda Environment**
   ```bash
   conda create -n financial-model python=3.10
   conda activate financial-model
   ```

3. **Install Dependencies**
   ```bash
   pip install -r src/new_app/requirements.txt
   ```

4. **Run Application**
   ```bash
   python src/new_app/main.py
   ```

## Configuration

### Application Configuration

1. **Create Configuration Directory**
   ```bash
   mkdir config
   ```

2. **Customize Settings** (Optional)
   - Copy `src/new_app/config/app_config.py` to `config/` and modify as needed
   - Adjust export formats, file paths, and UI settings

### Environment Variables

Set the following environment variables if needed:

```bash
# Optional: Custom data directory
export FINANCIAL_MODEL_DATA_DIR="/path/to/data"

# Optional: Custom output directory  
export FINANCIAL_MODEL_OUTPUT_DIR="/path/to/outputs"

# Optional: Log level
export FINANCIAL_MODEL_LOG_LEVEL="INFO"
```

## Security Configuration

### Password Protection

The application includes a security screen with password protection:

- **Default Password**: `agevolami2024`
- **To Change Password**: Modify the password check in `src/new_app/main.py`

```python
# In main.py, line ~25
if password == "your_new_password":
```

### Data Security

- All financial data is stored locally
- No data is transmitted to external servers
- Exported files are saved to local directories only

## Deployment Scenarios

### Scenario 1: Single User Desktop

**Use Case**: Individual consultant or analyst

**Setup**:
1. Follow Method 1 or 2 installation
2. Run directly from command line
3. Data stored in user directory

**Advantages**:
- Simple setup
- Full control over data
- No network dependencies

### Scenario 2: Multi-User Shared System

**Use Case**: Team of consultants sharing a workstation

**Setup**:
1. Install in shared location (e.g., `C:\Programs\FinancialModel\`)
2. Create shared data directory with appropriate permissions
3. Configure output directories per user

**Configuration**:
```python
# In app_config.py
data_directory = "D:/Shared/FinancialModel/Data"
output_directory = "D:/Shared/FinancialModel/Outputs/{username}"
```

### Scenario 3: Portable Installation

**Use Case**: Consultant working on multiple client sites

**Setup**:
1. Install on USB drive or portable storage
2. Use relative paths for all directories
3. Include Python portable installation

**Structure**:
```
PortableFinancialModel/
├── python/                 # Portable Python
├── app/                   # Application files
├── data/                  # Project data
├── outputs/               # Generated reports
└── run.bat               # Startup script
```

## Performance Optimization

### Memory Optimization

For large projects or Monte Carlo simulations:

```python
# In app_config.py
max_monte_carlo_simulations = 5000  # Reduce if memory limited
chart_dpi = 72  # Lower DPI for faster rendering
```

### Storage Optimization

```python
# In export_config.py
create_date_folders = False  # Reduce folder nesting
include_charts_in_export = False  # Reduce file sizes
```

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   ```bash
   # Ensure virtual environment is activated
   pip install -r requirements.txt
   ```

2. **Permission errors on Windows**
   ```bash
   # Run as administrator or adjust folder permissions
   ```

3. **Display issues on high-DPI screens**
   ```python
   # Add to main.py before ft.app()
   import os
   os.environ["FLET_WEB_RENDERER"] = "html"
   ```

4. **Memory errors during Monte Carlo**
   ```python
   # Reduce simulation count in monte_carlo_view.py
   self.n_simulations = 1000  # Instead of 10000
   ```

### Log Files

Application logs are stored in:
- **Windows**: `logs/app.log`
- **macOS/Linux**: `logs/app.log`

Check logs for detailed error information.

## Backup and Recovery

### Data Backup

Important directories to backup:
- `data/` - Project configurations
- `outputs/` - Generated reports
- `config/` - Custom configurations

### Automated Backup Script

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "backup_financial_model_$DATE.tar.gz" data/ outputs/ config/
```

## Updates and Maintenance

### Updating the Application

1. **Backup current installation**
2. **Download new version**
3. **Update dependencies**:
   ```bash
   pip install -r requirements.txt --upgrade
   ```
4. **Test with sample data**

### Maintenance Tasks

- **Weekly**: Clear old temporary files
- **Monthly**: Backup project data
- **Quarterly**: Update Python dependencies

## Support and Monitoring

### Health Checks

Create a simple health check script:

```python
# health_check.py
import sys
sys.path.append('src')

try:
    from new_app.models.client_profile import ClientProfile
    from new_app.models.project_assumptions import EnhancedProjectAssumptions
    print("✅ Application modules loaded successfully")
except ImportError as e:
    print(f"❌ Module import error: {e}")
    sys.exit(1)
```

### Performance Monitoring

Monitor these metrics:
- Application startup time
- Memory usage during analysis
- Export generation time
- File system space usage

## Contact Information

For deployment support:

**Abdelhalim Serhani**  
Financial & Business Consultant  
Agevolami SRL  
Website: www.agevolami.it & www.agevolami.ma  

*"Your way to explore crossborder opportunities and grow big"*
